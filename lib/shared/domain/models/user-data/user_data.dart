import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';

part 'user_data.freezed.dart';
part 'user_data.g.dart';

@freezed
sealed class UserData with _$UserData {
  const factory UserData({
    required String email,
    @Default(false) @Json<PERSON>ey(name: 'is_after_test') bool afterTest,
    @Json<PERSON>ey(name: 'last_pronunciation') LastCourse? lastPronunciation,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'last_conversation') LastCourse? lastConversation,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'last_listening') LastCourse? lastListening,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'last_speaking') LastCourse? lastSpeaking,
  }) = _UserData;

  factory UserData.fromJson(Map<String, dynamic> json) =>
      _$UserDataFromJson(json);
}

@freezed
sealed class LastCourse with _$LastCourse {
  const factory LastCourse({
    required DateTime accessTime,
    required String level,
    required int chapter,
    required SectionType section,
    required String path,
    @Default(SpeakingStage.stage1) SpeakingStage speakingStage,
    int? partOrder,
    int? subpartOrder,
  }) = _LastCourse;
  factory LastCourse.fromJson(Map<String, dynamic> json) =>
      _$LastCourseFromJson(json);
}

@freezed
sealed class LastCourseInfo with _$LastCourseInfo {
  const factory LastCourseInfo({required LastCourse info, dynamic data}) =
      _LastCourseInfo;
  factory LastCourseInfo.fromJson(Map<String, dynamic> json) =>
      _$LastCourseInfoFromJson(json);
}

@freezed
sealed class LessonResult with _$LessonResult {
  const factory LessonResult({
    int? partOrder,
    int? subpartOrder,
    SpeakingStage? speakingStage,
    required int contentOrder,
    required String path,
    required Map<String, dynamic> result,
  }) = _LessonResult;
  factory LessonResult.fromJson(Map<String, dynamic> json) =>
      _$LessonResultFromJson(json);
}

@freezed
sealed class PronunciationScore with _$PronunciationScore {
  const factory PronunciationScore({
    @Default(0) double accuracyScore,
    @Default(0) double fluencyScore,
    @Default(0) double prosodyScore,
    @Default(0) double completenessScore,
    @Default(0) double pronScore,
  }) = _PronunciationScore;
  factory PronunciationScore.fromJson(Map<String, dynamic> json) =>
      _$PronunciationScoreFromJson(json);
}

@freezed
sealed class PronunciationScoreParams with _$PronunciationScoreParams {
  const factory PronunciationScoreParams({
    required String level,
    required String chapter,
    int? partOrder,
    int? subpartOrder,
  }) = _PronunciationScoreParams;
  factory PronunciationScoreParams.fromJson(Map<String, dynamic> json) =>
      _$PronunciationScoreParamsFromJson(json);
}

@freezed
sealed class PronunciationAgregateScore with _$PronunciationAgregateScore {
  const factory PronunciationAgregateScore({
    @Default(0) double accuracyScore,
    @Default(0) double fluencyScore,
    @Default(0) double prosodyScore,
    @Default(0) double completenessScore,
    @Default(0) double pronScore,
    @Default(0) int dataCount,
  }) = _PronunciationAgregateScore;
  factory PronunciationAgregateScore.fromJson(Map<String, dynamic> json) =>
      _$PronunciationAgregateScoreFromJson(json);
}

@freezed
sealed class SpeakingAgregateScore with _$SpeakingAgregateScore {
  const factory SpeakingAgregateScore({
    @Default(0) double accuracyScore,
    @Default(0) double fluencyScore,
    @Default(0) double prosodyScore,
    @Default(0) double completenessScore,
    @Default(0) double pronScore,
    @Default(0) int dataCount,
  }) = _SpeakingAgregateScore;
  factory SpeakingAgregateScore.fromJson(Map<String, dynamic> json) =>
      _$SpeakingAgregateScoreFromJson(json);
}

enum SectionType { pronunciation, conversation, listening, speaking }
