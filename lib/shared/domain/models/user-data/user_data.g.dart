// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserData _$UserDataFromJson(Map<String, dynamic> json) => _UserData(
  email: json['email'] as String,
  afterTest: json['is_after_test'] as bool? ?? false,
  lastPronunciation:
      json['last_pronunciation'] == null
          ? null
          : LastCourse.fromJson(
            json['last_pronunciation'] as Map<String, dynamic>,
          ),
  lastConversation:
      json['last_conversation'] == null
          ? null
          : LastCourse.fromJson(
            json['last_conversation'] as Map<String, dynamic>,
          ),
  lastListening:
      json['last_listening'] == null
          ? null
          : LastCourse.fromJson(json['last_listening'] as Map<String, dynamic>),
  lastSpeaking:
      json['last_speaking'] == null
          ? null
          : LastCourse.fromJson(json['last_speaking'] as Map<String, dynamic>),
);

Map<String, dynamic> _$UserDataToJson(_UserData instance) => <String, dynamic>{
  'email': instance.email,
  'is_after_test': instance.afterTest,
  'last_pronunciation': instance.lastPronunciation,
  'last_conversation': instance.lastConversation,
  'last_listening': instance.lastListening,
  'last_speaking': instance.lastSpeaking,
};

_LastCourse _$LastCourseFromJson(Map<String, dynamic> json) => _LastCourse(
  accessTime: DateTime.parse(json['accessTime'] as String),
  level: json['level'] as String,
  chapter: (json['chapter'] as num).toInt(),
  section: $enumDecode(_$SectionTypeEnumMap, json['section']),
  path: json['path'] as String,
  speakingStage:
      $enumDecodeNullable(_$SpeakingStageEnumMap, json['speakingStage']) ??
      SpeakingStage.stage1,
  partOrder: (json['partOrder'] as num?)?.toInt(),
  subpartOrder: (json['subpartOrder'] as num?)?.toInt(),
);

Map<String, dynamic> _$LastCourseToJson(_LastCourse instance) =>
    <String, dynamic>{
      'accessTime': instance.accessTime.toIso8601String(),
      'level': instance.level,
      'chapter': instance.chapter,
      'section': _$SectionTypeEnumMap[instance.section]!,
      'path': instance.path,
      'speakingStage': _$SpeakingStageEnumMap[instance.speakingStage]!,
      'partOrder': instance.partOrder,
      'subpartOrder': instance.subpartOrder,
    };

const _$SectionTypeEnumMap = {
  SectionType.pronunciation: 'pronunciation',
  SectionType.conversation: 'conversation',
  SectionType.listening: 'listening',
  SectionType.speaking: 'speaking',
};

const _$SpeakingStageEnumMap = {
  SpeakingStage.stage1: 'stage1',
  SpeakingStage.onboardingStage2: 'onboardingStage2',
  SpeakingStage.stage2: 'stage2',
  SpeakingStage.onboardingStage3: 'onboardingStage3',
  SpeakingStage.stage3: 'stage3',
};

_LastCourseInfo _$LastCourseInfoFromJson(Map<String, dynamic> json) =>
    _LastCourseInfo(
      info: LastCourse.fromJson(json['info'] as Map<String, dynamic>),
      data: json['data'],
    );

Map<String, dynamic> _$LastCourseInfoToJson(_LastCourseInfo instance) =>
    <String, dynamic>{'info': instance.info, 'data': instance.data};

_LessonResult _$LessonResultFromJson(Map<String, dynamic> json) =>
    _LessonResult(
      partOrder: (json['partOrder'] as num?)?.toInt(),
      subpartOrder: (json['subpartOrder'] as num?)?.toInt(),
      speakingStage: $enumDecodeNullable(
        _$SpeakingStageEnumMap,
        json['speakingStage'],
      ),
      contentOrder: (json['contentOrder'] as num).toInt(),
      path: json['path'] as String,
      result: json['result'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$LessonResultToJson(_LessonResult instance) =>
    <String, dynamic>{
      'partOrder': instance.partOrder,
      'subpartOrder': instance.subpartOrder,
      'speakingStage': _$SpeakingStageEnumMap[instance.speakingStage],
      'contentOrder': instance.contentOrder,
      'path': instance.path,
      'result': instance.result,
    };

_PronunciationScore _$PronunciationScoreFromJson(Map<String, dynamic> json) =>
    _PronunciationScore(
      accuracyScore: (json['accuracyScore'] as num?)?.toDouble() ?? 0,
      fluencyScore: (json['fluencyScore'] as num?)?.toDouble() ?? 0,
      prosodyScore: (json['prosodyScore'] as num?)?.toDouble() ?? 0,
      completenessScore: (json['completenessScore'] as num?)?.toDouble() ?? 0,
      pronScore: (json['pronScore'] as num?)?.toDouble() ?? 0,
    );

Map<String, dynamic> _$PronunciationScoreToJson(_PronunciationScore instance) =>
    <String, dynamic>{
      'accuracyScore': instance.accuracyScore,
      'fluencyScore': instance.fluencyScore,
      'prosodyScore': instance.prosodyScore,
      'completenessScore': instance.completenessScore,
      'pronScore': instance.pronScore,
    };

_PronunciationScoreParams _$PronunciationScoreParamsFromJson(
  Map<String, dynamic> json,
) => _PronunciationScoreParams(
  level: json['level'] as String,
  chapter: json['chapter'] as String,
  partOrder: (json['partOrder'] as num?)?.toInt(),
  subpartOrder: (json['subpartOrder'] as num?)?.toInt(),
);

Map<String, dynamic> _$PronunciationScoreParamsToJson(
  _PronunciationScoreParams instance,
) => <String, dynamic>{
  'level': instance.level,
  'chapter': instance.chapter,
  'partOrder': instance.partOrder,
  'subpartOrder': instance.subpartOrder,
};

_PronunciationAgregateScore _$PronunciationAgregateScoreFromJson(
  Map<String, dynamic> json,
) => _PronunciationAgregateScore(
  accuracyScore: (json['accuracyScore'] as num?)?.toDouble() ?? 0,
  fluencyScore: (json['fluencyScore'] as num?)?.toDouble() ?? 0,
  prosodyScore: (json['prosodyScore'] as num?)?.toDouble() ?? 0,
  completenessScore: (json['completenessScore'] as num?)?.toDouble() ?? 0,
  pronScore: (json['pronScore'] as num?)?.toDouble() ?? 0,
  dataCount: (json['dataCount'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$PronunciationAgregateScoreToJson(
  _PronunciationAgregateScore instance,
) => <String, dynamic>{
  'accuracyScore': instance.accuracyScore,
  'fluencyScore': instance.fluencyScore,
  'prosodyScore': instance.prosodyScore,
  'completenessScore': instance.completenessScore,
  'pronScore': instance.pronScore,
  'dataCount': instance.dataCount,
};

_SpeakingAgregateScore _$SpeakingAgregateScoreFromJson(
  Map<String, dynamic> json,
) => _SpeakingAgregateScore(
  accuracyScore: (json['accuracyScore'] as num?)?.toDouble() ?? 0,
  fluencyScore: (json['fluencyScore'] as num?)?.toDouble() ?? 0,
  prosodyScore: (json['prosodyScore'] as num?)?.toDouble() ?? 0,
  completenessScore: (json['completenessScore'] as num?)?.toDouble() ?? 0,
  pronScore: (json['pronScore'] as num?)?.toDouble() ?? 0,
  dataCount: (json['dataCount'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$SpeakingAgregateScoreToJson(
  _SpeakingAgregateScore instance,
) => <String, dynamic>{
  'accuracyScore': instance.accuracyScore,
  'fluencyScore': instance.fluencyScore,
  'prosodyScore': instance.prosodyScore,
  'completenessScore': instance.completenessScore,
  'pronScore': instance.pronScore,
  'dataCount': instance.dataCount,
};
