import 'package:flutter/material.dart';

class VButtonGradient extends StatelessWidget {
  const VButtonGradient({
    super.key,
    required this.title,
    this.decoration,
    this.fontStyle,
    required this.onTap,
    this.isBorder = false,
  });

  final String title;
  final BoxDecoration? decoration; // User can override all styling
  final TextStyle? fontStyle;
  final VoidCallback onTap;
  final bool isBorder;

  // --- Styling Values (Original Colors Preserved) ---
  static const double _defaultHeight = 56.0; // Original height
  static final BorderRadius _defaultBorderRadius = BorderRadius.circular(
    14.0,
  ); // Original radius

  // Original gradient colors
  static const Color _gradientStartColor = Color(0xff802115);
  static const Color _gradientEndColor = Color(0xffEB1A19);

  // Original border color, slightly refined width for a more modern feel
  static const Color _borderColor = Color(0xff7D000B);
  static const double _borderWidth =
      2.5; // Original was 4, this is a bit sleeker

  // Modern shadow to add depth
  static final List<BoxShadow> _defaultShadow = [
    BoxShadow(
      color: Colors.black.withValues(alpha: .20), // A bit of shadow for depth
      spreadRadius: 1,
      blurRadius: 8,
      offset: const Offset(0, 4), // changes position of shadow
    ),
  ];

  @override
  Widget build(BuildContext context) {
    // Determine the decoration: user-provided or our modern default
    // This preserves the original colors while adding modern touches like shadow
    final BoxDecoration effectiveDecoration =
        decoration ??
        BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [_gradientStartColor, _gradientEndColor],
          ),
          borderRadius: _defaultBorderRadius,
          border:
              isBorder
                  ? Border.all(color: _borderColor, width: _borderWidth)
                  : null,
          boxShadow: _defaultShadow, // Added for modern depth
        );

    // Determine the text style: user-provided or our modern default
    final TextStyle effectiveTextStyle =
        fontStyle ??
        Theme.of(context).textTheme.titleMedium?.copyWith(
          color: Colors.white, // Good contrast on the red gradient
          fontWeight: FontWeight.w600, // Semi-bold for clarity
          letterSpacing: 0.5, // Slight spacing for modern feel
        ) ??
        const TextStyle(
          // Fallback if theme text style is unavailable
          color: Colors.white,
          fontSize: 16, // Reasonable default
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
        );

    return Container(
      decoration: effectiveDecoration,
      // Using ClipRRect to ensure the MaterialButton's ripple effect
      // is clipped to the container's border radius for a clean look.
      child: ClipRRect(
        // Use borderRadius from the effectiveDecoration if it's BorderRadius, otherwise default
        borderRadius:
            (effectiveDecoration.borderRadius as BorderRadius?) ??
            _defaultBorderRadius,
        child: MaterialButton(
          minWidth: double.infinity,
          height: _defaultHeight,
          onPressed: onTap,
          // MaterialButton's shape should match the container for ripple effect boundaries
          shape: RoundedRectangleBorder(
            borderRadius:
                (effectiveDecoration.borderRadius as BorderRadius?) ??
                _defaultBorderRadius,
          ),
          // Ensure MaterialButton itself is transparent to show container's gradient/decoration
          color: Colors.transparent,
          elevation: 0, // Shadow is handled by the Container's BoxDecoration
          focusElevation: 0,
          hoverElevation: 0,
          highlightElevation: 0,
          splashColor: Colors.white.withValues(
            alpha: 0.25,
          ), // Brighter splash for red
          highlightColor: Colors.white.withValues(
            alpha: 0.15,
          ), // Brighter highlight for red
          child: Text(
            title,
            softWrap: true, // Kept from original
            textAlign: TextAlign.center,
            style: effectiveTextStyle,
          ),
        ),
      ),
    );
  }
}
