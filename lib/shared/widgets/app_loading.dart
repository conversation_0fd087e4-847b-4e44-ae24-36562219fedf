import 'package:flutter/material.dart';
import 'dart:math' as math;

import 'package:selfeng/shared/globals.dart';

class AppLoading extends StatelessWidget {
  const AppLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xffFFF2F2),
              Color(0xffFDD8D8),
              Color(0xffFFECEC),
              Color(0xffFFFFFF),
            ],
            begin: Alignment.bottomLeft,
            end: Alignment.topRight,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              '$assetImageIcon/SELF-LOGO.png',
              // fit: BoxFit.fitWidth,
              width: 263,
              height: 263,
            ),
            const SizedBox(height: 52),
            const SpinningCircles(),
          ],
        ),
      ),
    );
  }
}

class SpinningCircles extends StatefulWidget {
  const SpinningCircles({super.key});

  @override
  _SpinningCirclesState createState() => _SpinningCirclesState();
}

class _SpinningCirclesState extends State<SpinningCircles>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: 50,
        height: 50,
        child: AnimatedBuilder(
          animation: _controller,
          builder: (_, child) {
            return Transform.rotate(
              angle: _controller.value * 2 * math.pi,
              child: child,
            );
          },
          child: CustomPaint(
            painter: CirclesPainter(),
            size: const Size(50, 50),
          ),
        ),
      ),
    );
  }
}

class CirclesPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..shader = const LinearGradient(
            colors: [Color(0xFFFE754C), Color(0xFFE21F29), Color(0xFFC3151F)],
          ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 8; // Adjust for circle size

    for (int i = 0; i < 4; i++) {
      final angle = i * (math.pi / 2); // Divide the circle into 4 parts
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);
      canvas.drawCircle(Offset(x, y), 8, paint); // Circle size is 16 (diameter)
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
