import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/firebase_options.dart';
import 'package:selfeng/main/app.dart';
import 'package:selfeng/main/app_env.dart';
import 'package:selfeng/services/notification_service/domain/providers/notification_service_provider.dart';
// import 'package:selfeng/main/observers.dart';

void main() => mainCommon(AppEnvironment.PROD);

/// Initialize the notification service using the new architecture
Future<void> _initializeNotificationService() async {
  final container = ProviderContainer();
  try {
    final notificationService = container.read(notificationServiceProvider);
    final result = await notificationService.initialize();

    result.fold(
      (error) {
        debugPrint(
          'Failed to initialize notification service: ${error.message}',
        );
        // Don't throw here to prevent app crash - notifications are not critical for app startup
      },
      (_) {
        debugPrint('Notification service initialized successfully');
      },
    );
  } finally {
    container.dispose();
  }
}

Future<void> mainCommon(AppEnvironment environment) async {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
  EnvInfo.initialize(environment);
  await dotenv.load(fileName: EnvInfo.envName);
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  await FirebaseAppCheck.instance.activate(
    androidProvider:
        kDebugMode ? AndroidProvider.debug : AndroidProvider.playIntegrity,
    appleProvider: kDebugMode ? AppleProvider.debug : AppleProvider.appAttest,
  );
  // Initialize notification service
  if (!kIsWeb) {
    await _initializeNotificationService();
  }

  runApp(
    ProviderScope(
      // observers: [if (kDebugMode) Observers()],
      child: const MyApp(),
    ),
  );
}
