import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:selfeng/firebase_options.dart';
import 'package:selfeng/services/fcm_service/domain/repositories/fcm_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Background message handler - must be a top-level function
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Handle background message processing here
  // You can add custom logic for background message handling
}

/// Repository for managing local and push notifications
class NotificationServiceRepository {
  final FCMServiceRepository _fcmService;
  final FirebaseMessaging _firebaseMessaging;

  FlutterLocalNotificationsPlugin? _flutterLocalNotificationsPlugin;
  AndroidNotificationChannel? _channel;
  bool _isInitialized = false;

  NotificationServiceRepository(this._fcmService, this._firebaseMessaging);

  /// Initialize the notification service
  Future<Either<AppException, void>> initialize() async {
    try {
      if (_isInitialized) {
        return const Right(null);
      }

      // Set background message handler
      FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

      // Initialize FCM token management
      final fcmResult = await _fcmService.initializeFCMToken();
      return fcmResult.fold((error) => Left(error), (_) async {
        // Request permissions
        final permissionResult = await _requestPermissions();
        return permissionResult.fold((error) => Left(error), (_) async {
          // Initialize local notifications
          await _initializeLocalNotifications();

          // Setup message handlers
          _setupMessageHandlers();

          // Configure foreground notification presentation
          await _firebaseMessaging.setForegroundNotificationPresentationOptions(
            alert: true,
            badge: true,
            sound: true,
          );

          // Subscribe to general notification topic by default
          await _subscribeToGeneralTopic();

          _isInitialized = true;
          return const Right(null);
        });
      });
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Notification service initialization failed',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Request necessary permissions
  Future<Either<AppException, void>> _requestPermissions() async {
    try {
      // Request FCM permissions
      final fcmPermissionResult = await _fcmService.requestPermissions();
      return fcmPermissionResult.fold((error) => Left(error), (_) async {
        // Request additional iOS permissions
        if (Platform.isIOS) {
          await Permission.contacts.request();
          await Permission.photos.request();
        }

        return const Right(null);
      });
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Permission request failed',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

    _channel = const AndroidNotificationChannel(
      'high_importance_channel',
      'High Importance Notifications',
      description: 'This channel is used for important notifications.',
      importance: Importance.high,
    );

    // Create Android notification channel
    await _flutterLocalNotificationsPlugin!
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(_channel!);

    // Initialize settings
    const androidSettings = AndroidInitializationSettings('ic_launcher');
    const iosSettings = DarwinInitializationSettings();
    const initializationSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _flutterLocalNotificationsPlugin!.initialize(initializationSettings);
  }

  /// Setup message handlers for foreground and app-opened scenarios
  void _setupMessageHandlers() {
    // Handle messages when app is in foreground
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle messages when app is opened from notification
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);
  }

  /// Handle foreground messages
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    await _showLocalNotification(message);
    await _incrementNotificationCount();
  }

  /// Handle messages when app is opened from notification
  Future<void> _handleMessageOpenedApp(RemoteMessage message) async {
    // Add custom logic for handling notification tap
    // For example, navigate to specific screen based on message data
    await _incrementNotificationCount();
  }

  /// Show local notification
  Future<void> _showLocalNotification(RemoteMessage message) async {
    final notification = message.notification;
    final android = message.notification?.android;

    if (notification != null &&
        android != null &&
        !kIsWeb &&
        _channel != null) {
      await _flutterLocalNotificationsPlugin?.show(
        notification.hashCode,
        notification.title,
        notification.body,
        NotificationDetails(
          android: AndroidNotificationDetails(
            _channel!.id,
            _channel!.name,
            channelDescription: _channel!.description,
            icon: 'ic_launcher',
          ),
        ),
        payload: notification.android?.imageUrl,
      );
    }
  }

  /// Increment notification count in shared preferences
  Future<void> _incrementNotificationCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentCount = prefs.getInt('notification') ?? 0;
      await prefs.setInt('notification', currentCount + 1);
    } catch (e) {
      // Log error but don't throw - this is not critical
      debugPrint('Failed to increment notification count: $e');
    }
  }

  /// Get notification count
  Future<Either<AppException, int>> getNotificationCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final count = prefs.getInt('notification') ?? 0;
      return Right(count);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Failed to get notification count',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Clear notification count
  Future<Either<AppException, void>> clearNotificationCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('notification');
      return const Right(null);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Failed to clear notification count',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Check if notifications are enabled
  Future<Either<AppException, bool>> areNotificationsEnabled() async {
    return await _fcmService.areNotificationsEnabled();
  }

  /// Refresh FCM token
  Future<Either<AppException, void>> refreshFCMToken() async {
    return await _fcmService.refreshFCMToken();
  }

  /// Remove FCM token for current device (useful for logout)
  Future<Either<AppException, void>> removeFCMToken() async {
    return await _fcmService.removeFCMTokenForCurrentDevice();
  }

  /// Subscribe to general notification topic by default
  Future<void> _subscribeToGeneralTopic() async {
    try {
      await _firebaseMessaging.subscribeToTopic('general_notification');
      debugPrint('✅ Subscribed to general_notification topic');
    } catch (e) {
      debugPrint('❌ Failed to subscribe to general_notification topic: $e');
      // Don't throw error - topic subscription failure shouldn't break initialization
    }
  }

  /// Subscribe to a specific topic
  Future<Either<AppException, void>> subscribeToTopic(String topic) async {
    return await _fcmService.subscribeToTopic(topic);
  }

  /// Unsubscribe from a specific topic
  Future<Either<AppException, void>> unsubscribeFromTopic(String topic) async {
    return await _fcmService.unsubscribeFromTopic(topic);
  }

  /// Unsubscribe from general notification topic (useful for logout)
  Future<Either<AppException, void>> unsubscribeFromGeneralTopic() async {
    return await unsubscribeFromTopic('general_notification');
  }
}
