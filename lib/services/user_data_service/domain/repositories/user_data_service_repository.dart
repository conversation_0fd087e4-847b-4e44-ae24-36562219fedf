import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/level.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

class UserDataServiceRepository {
  final FirestoreServiceRepository dataSource;
  UserDataServiceRepository(this.dataSource);

  Future<Either<AppException, UserData>> getUserData() async {
    try {
      final doc = await dataSource.dataUser().get();
      if (doc.data() is Map<String, dynamic>) {
        UserData userData = UserData.fromJson(
          doc.data() as Map<String, dynamic>,
        );
        return Right<AppException, UserData>(userData);
      } else {
        // throw Exception('User data not found');
        await dataSource.firebaseFunctions
            .httpsCallable('reinitUserData')
            .call();

        return getUserData();
      }
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Failed fetch user data',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  Future<Either<AppException, LastCourse>> updateLastCourse({
    required LastCourse lastCourse,
    required SectionType section,
  }) async {
    try {
      String field = '';
      switch (section) {
        case SectionType.pronunciation:
          field = 'last_pronunciation';
          break;
        case SectionType.conversation:
          field = 'last_conversation';
          break;
        case SectionType.listening:
          field = 'last_listening';
          break;
        case SectionType.speaking:
          field = 'last_speaking';
          break;
      }

      await dataSource.dataUser().update({field: lastCourse.toJson()});
      return Right<AppException, LastCourse>(lastCourse);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Update last course',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  Future<Either<AppException, dynamic>> saveLessonResult({
    required String level,
    required String chapter,
    required SectionType section,
    required LessonResult result,
  }) async {
    try {
      final String partOrder = (result.partOrder ?? 0).toString();
      final String subpartOrder = (result.subpartOrder ?? 0).toString();
      final String contentOrder = result.contentOrder.toString();
      final String speakingStage = result.speakingStage?.name ?? '';

      final String docId =
          '$level$chapter$partOrder$subpartOrder$contentOrder$speakingStage';

      await dataSource
          .dataUser()
          .collection('lessons')
          .doc(level)
          .collection('chapters')
          .doc(chapter)
          .collection('sections')
          .doc(section.name)
          .collection('results')
          .doc(docId)
          .set(result.toJson());

      return Right({'data': result});
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Failed save lesson result',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  Future<Either<AppException, List<LessonResult>>> getPronunciationResult(
    PronunciationScoreParams data,
  ) async {
    try {
      Query<Map<String, dynamic>> query = dataSource
          .dataUser()
          .collection('lessons')
          .doc(data.level)
          .collection('chapters')
          .doc(data.chapter)
          .collection('sections')
          .doc(SectionType.pronunciation.name)
          .collection('results');

      if (data.partOrder != null) {
        query = query.where('partOrder', isEqualTo: data.partOrder);
      }

      if (data.subpartOrder != null) {
        query = query.where('subpartOrder', isEqualTo: data.subpartOrder);
      }

      final querySnapshot = await query.get();

      if (querySnapshot.docs.isEmpty) {
        return const Right([]);
      }

      final results =
          querySnapshot.docs
              .map((doc) => LessonResult.fromJson(doc.data()))
              .toList();

      return Right(results);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Failed get pronunciation result',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  Future<Either<AppException, PronunciationAgregateScore>>
  calculatePronunciationResult(PronunciationScoreParams data) async {
    try {
      final resultsEither = await getPronunciationResult(data);

      return resultsEither.fold((error) => Left(error), (results) {
        if (results.isEmpty) {
          return Right(
            PronunciationAgregateScore(
              accuracyScore: 0,
              fluencyScore: 0,
              prosodyScore: 0,
              completenessScore: 0,
              pronScore: 0,
              dataCount: 0,
            ),
          );
        }

        final scores = results.map((result) => result.result).toList();

        double sumScore(String key) => scores
            .map((score) => score[key] as double)
            .fold<double>(0, (currentSum, score) => currentSum + score);

        return Right(
          PronunciationAgregateScore(
            dataCount: scores.length,
            accuracyScore: sumScore('accuracyScore'),
            fluencyScore: sumScore('fluencyScore'),
            prosodyScore: sumScore('prosodyScore'),
            completenessScore: sumScore('completenessScore'),
            pronScore: sumScore('pronScore'),
          ),
        );
      });
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Failed calculate pronunciation result',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  Future<Either<AppException, List<LessonResult>>> getConversationResult(
    PronunciationScoreParams data,
  ) async {
    try {
      final query = dataSource
          .dataUser()
          .collection('lessons')
          .doc(data.level)
          .collection('chapters')
          .doc(data.chapter)
          .collection('sections')
          .doc(SectionType.conversation.name)
          .collection('results');

      final querySnapshot = await query.get();

      if (querySnapshot.docs.isEmpty) {
        return const Right([]);
      }

      final results =
          querySnapshot.docs
              .map((doc) => LessonResult.fromJson(doc.data()))
              .toList();

      return Right(results);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Failed get conversation result',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  Future<Either<AppException, List<LessonResult>>> getListeningResult(
    PronunciationScoreParams data,
  ) async {
    try {
      final query = dataSource
          .dataUser()
          .collection('lessons')
          .doc(data.level)
          .collection('chapters')
          .doc(data.chapter)
          .collection('sections')
          .doc(SectionType.listening.name)
          .collection('results');

      final querySnapshot = await query.get();

      if (querySnapshot.docs.isEmpty) {
        return const Right([]);
      }

      final results =
          querySnapshot.docs
              .map((doc) => LessonResult.fromJson(doc.data()))
              .toList();

      return Right(results);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Failed get listening result',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  Future<Either<AppException, List<LessonResult>>> getSpeakingResult({
    required String level,
    required String chapter,
    required SpeakingStage stage,
  }) async {
    try {
      final querySnapshot =
          await dataSource
              .dataUser()
              .collection('lessons')
              .doc(level)
              .collection('chapters')
              .doc(chapter)
              .collection('sections')
              .doc(SectionType.speaking.name)
              .collection('results')
              .where('speakingStage', isEqualTo: stage.name)
              .get();

      final results =
          querySnapshot.docs
              .map((doc) => LessonResult.fromJson(doc.data()))
              .toList();

      return Right(results);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Failed get speaking result',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  Future<Either<AppException, SpeakingAgregateScore>> calculateSpeakingResult({
    required String level,
    required String chapter,
    required SpeakingStage stage,
  }) async {
    try {
      final resultsEither = await getSpeakingResult(
        level: level,
        chapter: chapter,
        stage: stage,
      );

      return resultsEither.fold((error) => Left(error), (results) {
        if (results.isEmpty) {
          return Right(
            SpeakingAgregateScore(
              accuracyScore: 0,
              fluencyScore: 0,
              prosodyScore: 0,
              completenessScore: 0,
              pronScore: 0,
              dataCount: 0,
            ),
          );
        }

        final scores = results.map((result) => result.result).toList();

        double sumScore(String key) => scores
            .map((score) => score[key] as double)
            .fold<double>(0, (currentSum, score) => currentSum + score);

        return Right(
          SpeakingAgregateScore(
            accuracyScore: sumScore('accuracyScore'),
            fluencyScore: sumScore('fluencyScore'),
            prosodyScore: sumScore('prosodyScore'),
            completenessScore: sumScore('completenessScore'),
            pronScore: sumScore('pronScore'),
            dataCount: scores.length,
          ),
        );
      });
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Failed calculate speaking result',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  void setLevelCompleted({required String level}) {}

  void setChapterCompleted({required String level, required String chapter}) {}

  Future<Either<AppException, dynamic>> setSectionCompleted({
    required String level,
    required String chapter,
    required SectionType section,
  }) async {
    try {
      await dataSource
          .dataUser()
          .collection('lessons')
          .doc(level)
          .collection('chapters')
          .doc(chapter)
          .collection('sections')
          .doc(section.name)
          .set({'isComplete': true});

      return Right({'data': 'ok'});
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Failed save section complete',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  Future<Either<AppException, bool>> isLevelCompleted({
    required Level level,
  }) async {
    try {
      final query =
          dataSource.dataUser().collection('lessons').doc(level.name).get();

      final doc = await query;

      if (!doc.exists) {
        return Right(false);
      }
      return Right(doc.data()!['isComplete']);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Failed check level complete',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Save FCM token for the current user and device
  Future<Either<AppException, void>> saveFCMToken({
    required String token,
    required String deviceId,
  }) async {
    try {
      // Debug: Check if user is authenticated
      final currentUser = dataSource.firebaseAuth.currentUser;
      if (currentUser == null) {
        return Left(
          AppException(
            identifier: 'User not authenticated',
            statusCode: 401,
            message: 'User must be authenticated to save FCM token',
          ),
        );
      }

      debugPrint('Saving FCM token for user: ${currentUser.uid}');
      debugPrint('Device ID: $deviceId');
      debugPrint('Token: ${token.substring(0, 20)}...');

      await dataSource.dataUser().set({
        'fcm_tokens': {
          deviceId: {
            'token': token,
            'updated_at': FieldValue.serverTimestamp(),
            'platform': Platform.isIOS ? 'ios' : 'android',
          },
        },
      }, SetOptions(merge: true));

      debugPrint('FCM token saved successfully');
      return const Right(null);
    } catch (e) {
      debugPrint('Error saving FCM token: $e');
      return Left(
        AppException(
          identifier: 'Failed to save FCM token',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Remove FCM token for a specific device
  Future<Either<AppException, void>> removeFCMToken({
    required String deviceId,
  }) async {
    try {
      await dataSource.dataUser().update({
        'fcm_tokens.$deviceId': FieldValue.delete(),
      });

      return const Right(null);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Failed to remove FCM token',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Get all FCM tokens for the current user
  Future<Either<AppException, Map<String, dynamic>>> getFCMTokens() async {
    try {
      final doc = await dataSource.dataUser().get();

      if (!doc.exists || doc.data() == null) {
        return const Right({});
      }

      final data = doc.data() as Map<String, dynamic>;
      final fcmTokens = data['fcm_tokens'] as Map<String, dynamic>? ?? {};

      return Right(fcmTokens);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Failed to get FCM tokens',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }
}
