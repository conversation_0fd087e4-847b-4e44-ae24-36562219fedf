import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:selfeng/services/user_data_service/domain/repositories/user_data_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

/// Repository for managing FCM tokens and device information
class FCMServiceRepository {
  final UserDataServiceRepository _userDataService;
  final FirebaseMessaging _firebaseMessaging;
  final DeviceInfoPlugin _deviceInfo;

  FCMServiceRepository(
    this._userDataService,
    this._firebaseMessaging,
    this._deviceInfo,
  );

  /// Initialize FCM token management
  /// This should be called during app initialization
  Future<Either<AppException, void>> initializeFCMToken() async {
    try {
      // Check if user is authenticated first
      final currentUser = _userDataService.dataSource.firebaseAuth.currentUser;
      if (currentUser == null) {
        debugPrint('User not authenticated, skipping FCM token save for now');
        // Still setup token refresh listener for when user logs in
        _setupTokenRefreshListener();
        return const Right(null);
      }

      // Get current FCM token
      final tokenResult = await getFCMToken();
      return tokenResult.fold((error) => Left(error), (token) async {
        if (token.isEmpty) {
          return Left(
            AppException(
              identifier: 'FCM token initialization failed',
              statusCode: 0,
              message: 'Unable to retrieve FCM token',
            ),
          );
        }

        // Get device ID
        final deviceIdResult = await getDeviceId();
        return deviceIdResult.fold((error) => Left(error), (deviceId) async {
          if (deviceId.isEmpty) {
            return Left(
              AppException(
                identifier: 'Device ID retrieval failed',
                statusCode: 0,
                message: 'Unable to retrieve device ID',
              ),
            );
          }

          // Save token to user data
          final saveResult = await _userDataService.saveFCMToken(
            token: token,
            deviceId: deviceId,
          );

          return saveResult.fold((error) => Left(error), (_) {
            // Listen for token refresh
            _setupTokenRefreshListener();
            return const Right(null);
          });
        });
      });
    } catch (e) {
      return Left(
        AppException(
          identifier: 'FCM initialization error',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Get the current FCM token
  Future<Either<AppException, String>> getFCMToken() async {
    try {
      if (kIsWeb) {
        final token = await _firebaseMessaging.getToken(
          vapidKey:
              'BMG7zTqKq2DsHcbNaHSjhHSrJ1UTuy-5OJsLKXGSNazIpnph0JhAx2wqpBymDC6D3cRVt30RF1jAfFZxu5l0AcE',
        );
        return Right(token ?? '');
      } else {
        final token = await _firebaseMessaging.getToken();
        return Right(token ?? '');
      }
    } catch (e) {
      return Left(
        AppException(
          identifier: 'FCM token retrieval failed',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Get unique device identifier
  Future<Either<AppException, String>> getDeviceId() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return Right(androidInfo.id);
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return Right(iosInfo.identifierForVendor ?? '');
      } else {
        return Left(
          AppException(
            identifier: 'Unsupported platform',
            statusCode: 0,
            message: 'Device ID not available for this platform',
          ),
        );
      }
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Device ID retrieval failed',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Refresh and save the current FCM token
  Future<Either<AppException, void>> refreshFCMToken() async {
    try {
      final tokenResult = await getFCMToken();
      return tokenResult.fold((error) => Left(error), (token) async {
        final deviceIdResult = await getDeviceId();
        return deviceIdResult.fold(
          (error) => Left(error),
          (deviceId) =>
              _userDataService.saveFCMToken(token: token, deviceId: deviceId),
        );
      });
    } catch (e) {
      return Left(
        AppException(
          identifier: 'FCM token refresh failed',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Remove FCM token for current device
  Future<Either<AppException, void>> removeFCMTokenForCurrentDevice() async {
    try {
      final deviceIdResult = await getDeviceId();
      return deviceIdResult.fold(
        (error) => Left(error),
        (deviceId) => _userDataService.removeFCMToken(deviceId: deviceId),
      );
    } catch (e) {
      return Left(
        AppException(
          identifier: 'FCM token removal failed',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Get all FCM tokens for the current user
  Future<Either<AppException, Map<String, dynamic>>> getAllFCMTokens() async {
    return await _userDataService.getFCMTokens();
  }

  /// Setup listener for FCM token refresh
  void _setupTokenRefreshListener() {
    _firebaseMessaging.onTokenRefresh.listen((newToken) async {
      final deviceIdResult = await getDeviceId();
      deviceIdResult.fold(
        (error) => debugPrint(
          'Failed to get device ID for token refresh: ${error.message}',
        ),
        (deviceId) async {
          await _userDataService.saveFCMToken(
            token: newToken,
            deviceId: deviceId,
          );
        },
      );
    });
  }

  /// Request notification permissions
  Future<Either<AppException, NotificationSettings>>
  requestPermissions() async {
    try {
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: true,
        badge: true,
        carPlay: true,
        criticalAlert: true,
        provisional: false,
        sound: true,
      );

      return Right(settings);
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Permission request failed',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Check if notifications are enabled
  Future<Either<AppException, bool>> areNotificationsEnabled() async {
    try {
      final settings = await _firebaseMessaging.getNotificationSettings();
      return Right(
        settings.authorizationStatus == AuthorizationStatus.authorized,
      );
    } catch (e) {
      return Left(
        AppException(
          identifier: 'Permission check failed',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }

  /// Initialize FCM token after user authentication
  /// Call this method after user successfully logs in
  Future<Either<AppException, void>> initializeFCMTokenAfterAuth() async {
    try {
      // Check if user is authenticated
      final currentUser = _userDataService.dataSource.firebaseAuth.currentUser;
      if (currentUser == null) {
        return Left(
          AppException(
            identifier: 'User not authenticated',
            statusCode: 401,
            message: 'User must be authenticated to save FCM token',
          ),
        );
      }

      debugPrint(
        'Initializing FCM token after authentication for user: ${currentUser.uid}',
      );

      // Get current FCM token
      final tokenResult = await getFCMToken();
      return tokenResult.fold((error) => Left(error), (token) async {
        if (token.isEmpty) {
          return Left(
            AppException(
              identifier: 'FCM token initialization failed',
              statusCode: 0,
              message: 'Unable to retrieve FCM token',
            ),
          );
        }

        // Get device ID
        final deviceIdResult = await getDeviceId();
        return deviceIdResult.fold((error) => Left(error), (deviceId) async {
          if (deviceId.isEmpty) {
            return Left(
              AppException(
                identifier: 'Device ID retrieval failed',
                statusCode: 0,
                message: 'Unable to retrieve device ID',
              ),
            );
          }

          // Save token to user data
          return await _userDataService.saveFCMToken(
            token: token,
            deviceId: deviceId,
          );
        });
      });
    } catch (e) {
      return Left(
        AppException(
          identifier: 'FCM token initialization after auth failed',
          statusCode: 0,
          message: e.toString(),
        ),
      );
    }
  }
}
