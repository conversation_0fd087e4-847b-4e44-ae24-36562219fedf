import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/services/fcm_service/domain/providers/fcm_service_provider.dart';

/// Debug helper for testing FCM token functionality
class FCMDebugHelper {
  /// Test FCM token initialization after authentication
  /// Call this after user login to test if FCM tokens can be saved
  static Future<void> testFCMTokenAfterAuth(WidgetRef ref) async {
    try {
      debugPrint('=== FCM Debug Test Started ===');
      
      final fcmService = ref.read(fcmServiceProvider);
      
      // Test FCM token initialization after auth
      final result = await fcmService.initializeFCMTokenAfterAuth();
      
      result.fold(
        (error) {
          debugPrint('❌ FCM Token Test Failed:');
          debugPrint('   Error: ${error.identifier}');
          debugPrint('   Message: ${error.message}');
          debugPrint('   Status Code: ${error.statusCode}');
        },
        (_) {
          debugPrint('✅ FCM Token Test Successful');
        },
      );
      
      debugPrint('=== FCM Debug Test Completed ===');
    } catch (e) {
      debugPrint('❌ FCM Debug Test Exception: $e');
    }
  }

  /// Test getting FCM token without saving
  static Future<void> testGetFCMToken(WidgetRef ref) async {
    try {
      debugPrint('=== FCM Token Retrieval Test Started ===');
      
      final fcmService = ref.read(fcmServiceProvider);
      
      final tokenResult = await fcmService.getFCMToken();
      
      tokenResult.fold(
        (error) {
          debugPrint('❌ FCM Token Retrieval Failed:');
          debugPrint('   Error: ${error.identifier}');
          debugPrint('   Message: ${error.message}');
        },
        (token) {
          debugPrint('✅ FCM Token Retrieved Successfully');
          debugPrint('   Token: ${token.substring(0, 20)}...');
        },
      );
      
      debugPrint('=== FCM Token Retrieval Test Completed ===');
    } catch (e) {
      debugPrint('❌ FCM Token Retrieval Test Exception: $e');
    }
  }

  /// Test device ID retrieval
  static Future<void> testGetDeviceId(WidgetRef ref) async {
    try {
      debugPrint('=== Device ID Test Started ===');
      
      final fcmService = ref.read(fcmServiceProvider);
      
      final deviceIdResult = await fcmService.getDeviceId();
      
      deviceIdResult.fold(
        (error) {
          debugPrint('❌ Device ID Retrieval Failed:');
          debugPrint('   Error: ${error.identifier}');
          debugPrint('   Message: ${error.message}');
        },
        (deviceId) {
          debugPrint('✅ Device ID Retrieved Successfully');
          debugPrint('   Device ID: $deviceId');
        },
      );
      
      debugPrint('=== Device ID Test Completed ===');
    } catch (e) {
      debugPrint('❌ Device ID Test Exception: $e');
    }
  }
}
