import 'dart:ui';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router.dart';
import 'package:selfeng/features/setting/presentation/providers/setting_controller.dart';
import 'package:selfeng/l10n/generated/app_localizations.dart';
import 'package:selfeng/main/app_env.dart';
import 'package:selfeng/shared/theme/app_theme.dart';

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  // final appRouter = AppRouter();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(appThemeProvider);
    final settingState = ref.watch(settingControllerProvider);
    final router = ref.watch(routerProvider);

    // Notification service is now initialized in main.dart during app startup
    if (!kIsWeb) {
      DartPluginRegistrant.ensureInitialized();
    }

    FirebaseAuth.instance.authStateChanges().listen((User? user) {
      router.refresh();
    });
    return MaterialApp.router(
      routerConfig: router,
      title: EnvInfo.appName,
      theme: AppTheme.lightTheme.copyWith(
        pageTransitionsTheme: const PageTransitionsTheme(
          builders: {
            TargetPlatform.android: CupertinoPageTransitionsBuilder(),
            TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
          },
        ),
      ),
      darkTheme: AppTheme.darkTheme.copyWith(
        pageTransitionsTheme: const PageTransitionsTheme(
          builders: {
            TargetPlatform.android: CupertinoPageTransitionsBuilder(),
            TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
          },
        ),
      ),
      themeMode: themeMode,
      localizationsDelegates: AppLocalizations.localizationsDelegates,
      supportedLocales:
          settingState.value?.localeSupport ?? [const Locale('en', 'US')],
      locale: settingState.value?.locale ?? const Locale('en', 'US'),
      debugShowCheckedModeBanner: false,
      onGenerateTitle: (BuildContext context) => EnvInfo.appName,
    );
  }
}
