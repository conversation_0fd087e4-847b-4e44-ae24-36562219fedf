import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/setting/presentation/providers/setting_controller.dart';
import 'package:selfeng/features/setting/presentation/providers/state/setting_state.dart';
import 'package:selfeng/features/setting/presentation/widgets/select_language.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';
import 'package:go_router/go_router.dart';

class LanguageScreen extends ConsumerStatefulWidget {
  final String origin;
  const LanguageScreen({super.key, required this.origin});

  @override
  ConsumerState<LanguageScreen> createState() => _LanguageScreenState();
}

class _LanguageScreenState extends ConsumerState<LanguageScreen> {
  late AsyncValue<SettingState> viewState;
  late SettingController viewModel;

  @override
  Widget build(BuildContext context) {
    viewState = ref.watch(settingControllerProvider);
    viewModel = ref.watch(settingControllerProvider.notifier);
    return Scaffold(
      body: Stack(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0xffFFFFFF),
                  Color(0xffFFECEC),
                  Color(0xffFDD8D8),
                  Color(0xffFFF2F2),
                ],
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 82),
                Text(
                  context.loc.selectLanguage,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 4),
                Text(
                  context.loc.selectLanguageDes,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: const Color(0xff998E8D),
                  ),
                ),
                const SizedBox(height: 36),
                SelectLanguage(
                  title: viewState.value!.languageList[0].title,
                  icon: viewState.value!.languageList[0].icon,
                  groupValue: viewState.value?.selectedIndex,
                  value: 0,
                  onTap: (val) {
                    viewModel.saveLocale(val);
                  },
                ),
                const SizedBox(height: 24),
                SelectLanguage(
                  title: viewState.value!.languageList[1].title,
                  icon: viewState.value!.languageList[1].icon,
                  groupValue: viewState.value?.selectedIndex,
                  value: 1,
                  onTap: (val) {
                    viewModel.saveLocale(val);
                  },
                ),
              ],
            ),
          ),
          Align(
            alignment: FractionalOffset.bottomCenter,
            child: Image.asset(
              viewState
                  .value!
                  .languageList[viewState.value!.selectedIndex]
                  .imageBackground,
              fit: BoxFit.fitWidth,
              width: double.infinity,
            ),
          ),
          Align(
            alignment: FractionalOffset.bottomCenter,
            child: Container(
              margin: EdgeInsets.symmetric(vertical: 32, horizontal: 16),
              child: VButtonGradient(
                title: context.loc.choose,
                onTap: () async {
                  if (widget.origin == 'profile') {
                    context.pop();
                  } else {
                    customNav(
                      context,
                      RouterName.selectedLanguageScreen,
                      isReplace: true,
                    );
                  }
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
