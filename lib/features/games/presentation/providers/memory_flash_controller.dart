// observe_recall_controller.dart
import 'dart:async';
import 'dart:math';

import 'package:flutter/services.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/games/domain/models/game_word.dart';
import 'package:selfeng/features/games/domain/providers/game_provider.dart';
import 'package:selfeng/features/games/domain/repositories/game_repository.dart';
import 'package:selfeng/features/games/presentation/providers/state/memory_flash_state.dart';
import 'package:selfeng/services/timer_cache_service/presentation/providers/timer_controller.dart';

part 'memory_flash_controller.g.dart';

@riverpod
class MemoryFlashController extends _$MemoryFlashController {
  late GameRepository gameRepository;
  Timer? _gameTimer;
  final Random _random = Random();
  late TimerController viewTimerModel;
  bool get isTimer {
    final value = state.valueOrNull;
    if (value == null) return false;
    return value.gameStage == GameStage.observe ||
        value.gameStage == GameStage.recall;
  }

  bool get isChooseGame {
    final value = state.valueOrNull;
    if (value == null) return false;
    return value.gameStage == GameStage.topic;
  }

  bool isLoaded = false;

  @override
  FutureOr<MemoryFlashState> build({String? topic}) async {
    // Set landscape orientation when controller is initialized
    // SystemChrome.setPreferredOrientations([
    //   DeviceOrientation.landscapeLeft,
    //   DeviceOrientation.landscapeRight,
    // ]);

    // Clean up when the controller is disposed
    ref.onDispose(_cleanupTimers);

    viewTimerModel = ref.watch(timerControllerProvider.notifier);
    gameRepository = ref.watch(gameRepositoryProvider);

    _init();

    return MemoryFlashState();
  }

  void _cleanupTimers() {
    _gameTimer?.cancel();
    _gameTimer = null;
  }

  Future<void> _init() async {
    try {
      await Future.delayed(Duration(seconds: 1));

      state = AsyncLoading();

      // Check if topic is null before using it
      if (topic == null) {
        state = AsyncError('Topic is required', StackTrace.current);
        return;
      }

      final contents = await gameRepository.getTopic(references: topic!);
      contents.fold(
        (failure) {
          state = AsyncError(failure.message, StackTrace.current);
        },
        (data) async {
          try {
            state = AsyncData(MemoryFlashState(topic: data));
            selectTopic();
          } catch (error, stackTrace) {
            state = AsyncError(
              'Failed to initialize game state: $error',
              stackTrace,
            );
          }
        },
      );
    } catch (error, stackTrace) {
      state = AsyncError('Initialization failed: $error', stackTrace);
    }
  }

  Future<void> loadGameContent() async {
    try {
      if (isLoaded) return;
      isLoaded = true;

      final currentState = state.valueOrNull;
      if (currentState == null) {
        state = AsyncError('Game state is not initialized', StackTrace.current);
        return;
      }

      if (currentState.memoryFlash.isNotEmpty) {
        startGame();
        return;
      }

      // Check if topic is null before using it
      if (topic == null) {
        state = AsyncError(
          'Topic is required for loading content',
          StackTrace.current,
        );
        return;
      }

      final contents = await gameRepository.getListTextMemoryFlash(
        referencesId: topic!,
      );

      contents.fold(
        (failure) {
          state = AsyncError(failure.message, StackTrace.current);
        },
        (data) async {
          try {
            if (data.isEmpty) {
              state = AsyncError(
                "The topic that you select not available, please choose another topic.",
                StackTrace.current,
              );
              // Wait a moment then reset to topic selection
              try {
                await Future.delayed(const Duration(seconds: 2), () {
                  // state = AsyncData(
                  //   MemoryFlashState(
                  //     topics: state.value?.topics ?? [],
                  //   ).copyWith(gameStage: GameStage.topic),
                  // );
                });
              } catch (delayError) {
                // Handle delay errors silently as they're not critical
              }
              return;
            }

            final stateValue = state.valueOrNull;
            if (stateValue != null) {
              state = AsyncData(stateValue.copyWith(memoryFlash: data));
              startGame();
            }
          } catch (error, stackTrace) {
            state = AsyncError(
              'Failed to process game content: $error',
              stackTrace,
            );
          }
        },
      );
    } catch (error, stackTrace) {
      isLoaded = false; // Reset loading flag on error
      state = AsyncError('Failed to load game content: $error', stackTrace);
    }
  }

  void startGame() {
    try {
      final currentState = state.valueOrNull;
      if (currentState == null) {
        state = AsyncError(
          'Cannot start game: state is null',
          StackTrace.current,
        );
        return;
      }

      state = AsyncData(
        currentState.copyWith(isPlaying: false, gameStage: GameStage.observe),
      );

      final updatedState = state.valueOrNull;
      if (updatedState == null || updatedState.isPlaying) return;

      // Check bounds before accessing array
      if (updatedState.memoryFlash.isEmpty ||
          updatedState.currentLevelIndex >= updatedState.memoryFlash.length) {
        state = AsyncError(
          'Invalid game level or empty memory flash data',
          StackTrace.current,
        );
        return;
      }

      viewTimerModel.setTotalSecond(
        seconds: 25,
        // updatedState.memoryFlash[updatedState.currentLevelIndex].countdown ??
        // 21,
      );
      viewTimerModel.startTimer();

      final currentLevel =
          updatedState.memoryFlash[updatedState.currentLevelIndex];
      if (currentLevel.listText.isEmpty) {
        state = AsyncError(
          'No text available for current level',
          StackTrace.current,
        );
        return;
      }

      List<String> shuffleWord = _getRandomWords(currentLevel.listText, 10);
      shuffleWord.shuffle();

      state = AsyncData(
        updatedState.copyWith(
          isPlaying: true,
          score: 0,
          words: shuffleWord,
          wordRandom: _getRandomWords(shuffleWord, 7),
        ),
      );

      // Animation timing initialization removed as it's not used

      // Get the list of random words
      final finalState = state.valueOrNull;
      if (finalState == null) return;

      List<String> randomWords = finalState.wordRandom;
      if (randomWords.isEmpty) {
        state = AsyncError('No random words generated', StackTrace.current);
        return;
      }

      int currentWordIndex = 1;

      // Show first word immediately
      _showWord(randomWords[0]);

      // Start a timer to show words sequentially
      _gameTimer = Timer.periodic(
        const Duration(seconds: 3, microseconds: 500),
        (timer) {
          try {
            if (currentWordIndex >= randomWords.length) {
              // All words have been shown, stop the game
              timer.cancel();
              stopGame();
              return;
            } else {
              _showWord(randomWords[currentWordIndex]);
              currentWordIndex++;
            }
          } catch (error) {
            // Handle timer callback errors
            timer.cancel();
            state = AsyncError('Game timer error: $error', StackTrace.current);
          }
        },
      );
    } catch (error, stackTrace) {
      state = AsyncError('Failed to start game: $error', stackTrace);
    }
  }

  void _showWord(String word) {
    try {
      final currentState = state.valueOrNull;
      if (currentState == null) {
        state = AsyncError(
          'Cannot show word: state is null',
          StackTrace.current,
        );
        return;
      }

      // Create a word object with random vertical position
      final verticalPosition =
          _random.nextDouble() * 0.8 + 0.1; // Between 10% and 80% of height

      final newWord = WordObject(
        word: word,
        verticalPosition: verticalPosition,
        horizontalPosition: -0.2, // Start slightly off-screen
      );

      // Update state with the new word
      final currentActiveWords = List<WordObject>.from(
        currentState.activeWords,
      );
      currentActiveWords.add(newWord);

      state = AsyncData(currentState.copyWith(activeWords: currentActiveWords));
    } catch (error, stackTrace) {
      state = AsyncError('Failed to show word: $error', stackTrace);
    }
  }

  void stopGame() {
    try {
      _gameTimer?.cancel();
      _gameTimer = null;

      viewTimerModel.setTotalSecond(seconds: 59);
      viewTimerModel.startTimer();

      final currentState = state.valueOrNull;
      if (currentState != null) {
        state = AsyncData(
          currentState.copyWith(isPlaying: false, gameStage: GameStage.recall),
        );
      }
    } catch (error, stackTrace) {
      state = AsyncError('Failed to stop game: $error', stackTrace);
    }
  }

  void exitGame() {
    // Stop the game if it's running
    if (state.value!.isPlaying) {
      _gameTimer?.cancel();
    }
  }

  void selectWord(String selectedWord) {
    try {
      final currentState = state.valueOrNull;
      if (currentState == null) {
        state = AsyncError(
          'Cannot select word: state is null',
          StackTrace.current,
        );
        return;
      }

      if (currentState.wordChoices.length == 7) {
        submitAnswers();
        // state = AsyncError('Max selected words', StackTrace.current);
      } else {
        final currentChoices = List<String>.from(currentState.wordChoices);
        if (currentChoices.contains(selectedWord)) {
          // currentChoices.removeWhere((word) => word == selectedWord);
        } else {
          currentChoices.add(selectedWord);
        }

        state = AsyncData(currentState.copyWith(wordChoices: currentChoices));
        if (currentChoices.length == 7) {
          submitAnswers();
        }
      }
    } catch (error, stackTrace) {
      state = AsyncError('Failed to select word: $error', stackTrace);
    }
  }

  void changeTopic({required int index}) {
    try {
      final currentState = state.valueOrNull;
      if (currentState != null) {
        state = AsyncData(currentState.copyWith(selectedTopic: index));
      }
    } catch (error, stackTrace) {
      state = AsyncError('Failed to change topic: $error', stackTrace);
    }
  }

  bool nextLevel() {
    try {
      final currentState = state.valueOrNull;
      if (currentState == null) {
        state = AsyncError(
          'Cannot proceed to next level: state is null',
          StackTrace.current,
        );
        return false;
      }

      state = AsyncData(currentState.copyWith(wordChoices: []));

      // Check bounds before accessing array
      if (currentState.memoryFlash.isEmpty) {
        state = AsyncError(
          'No memory flash data available',
          StackTrace.current,
        );
        return false;
      }

      if (currentState.currentLevelIndex <
          currentState.memoryFlash.length - 1) {
        final updatedState = state.valueOrNull;
        if (updatedState != null) {
          state = AsyncData(
            updatedState.copyWith(
              currentLevelIndex: updatedState.currentLevelIndex + 1,
            ),
          );
          selectTopic();
          return true;
        }
      }
      // } else if (currentState.currentTopicIndex <
      //     currentState.topic.length - 1) {
      //   state = AsyncData(currentState.copyWith(currentLevelIndex: 0, memoryFlash: []));
      //   nextTopic();
      //   selectTopic();

      return false;
    } catch (error, stackTrace) {
      state = AsyncError('Failed to proceed to next level: $error', stackTrace);
      return false;
    }
  }

  void gameOver() {
    try {
      final currentState = state.valueOrNull;
      if (currentState != null) {
        state = AsyncData(currentState.copyWith(gameStage: GameStage.gameOver));
        Future.delayed(
          Duration(seconds: 5),
        ).then((e) => submitAnswers()).catchError((error) {
          state = AsyncError(
            'Game over delay failed: $error',
            StackTrace.current,
          );
        });
      }
    } catch (error, stackTrace) {
      state = AsyncError('Failed to trigger game over: $error', stackTrace);
    }
  }

  Future<void> submitAnswers() async {
    try {
      viewTimerModel.stopTimer();
      _gameTimer?.cancel();
      _gameTimer = null;

      final currentState = state.valueOrNull;
      if (currentState == null) {
        state = AsyncError(
          'Cannot submit answers: state is null',
          StackTrace.current,
        );
        return;
      }

      int finalScore = 0;

      // Calculate score based on correct words selected
      for (String word in currentState.wordRandom) {
        if (currentState.wordChoices.contains(word)) {
          finalScore += 1;
        }
      }

      state = AsyncData(
        currentState.copyWith(score: finalScore, gameStage: GameStage.result),
      );

      // Check bounds before accessing array
      if (currentState.memoryFlash.isNotEmpty) {
        await gameRepository.saveResultMemoryFlash(
          references: currentState.memoryFlash[0].references ?? '',
          score: finalScore,
        );
      }
    } catch (error, stackTrace) {
      state = AsyncError('Failed to submit answers: $error', stackTrace);
    }
  }

  void finishGame() {
    try {
      final currentState = state.valueOrNull;
      if (currentState != null) {
        state = AsyncData(currentState.copyWith(gameStage: GameStage.finished));
      }
    } catch (error, stackTrace) {
      state = AsyncError('Failed to finish game: $error', stackTrace);
    }
  }

  void resetGame() {
    try {
      // Stop any running timers first
      _gameTimer?.cancel();
      _gameTimer = null;
      viewTimerModel.stopTimer();

      // Reset loading flag to force content reload
      isLoaded = false;

      final currentState = state.valueOrNull;
      if (currentState == null) {
        state = AsyncError(
          'Cannot reset game: state is null',
          StackTrace.current,
        );
        return;
      }

      // Reset game state to initial values while preserving topic
      state = AsyncData(
        currentState.copyWith(
          gameStage: GameStage.loading, // Start from loading stage
          wordChoices: [],
          activeWords: [],
          words: [],
          wordRandom: [],
          score: 0,
          isPlaying: false,
          currentLevelIndex: 0,
          // Keep topic and memoryFlash data but clear it to force reload
          memoryFlash: [],
        ),
      );

      // Start the proper game flow with timer
      // selectTopic() will:
      // 1. Set gameStage to GameStage.loading
      // 2. Set timer to 3 seconds and start it
      // 3. When timer expires, loadGameContent() will be called
      // 4. loadGameContent() will reload data and call startGame()
      // 5. startGame() will set the observe timer (25 seconds)
      selectTopic();
    } catch (error, stackTrace) {
      state = AsyncError('Failed to reset game: $error', stackTrace);
    }
  }

  List<String> _getRandomWords(List<String> words, int count) {
    // Create a copy of the original list to avoid modifying it
    final wordsCopy = List<String>.from(words);
    final random = Random();
    final result = <String>[];

    // Make sure we don't try to select more words than available
    final selectionCount = count > wordsCopy.length ? wordsCopy.length : count;

    for (int i = 0; i < selectionCount; i++) {
      final randomIndex = random.nextInt(wordsCopy.length);
      result.add(wordsCopy[randomIndex]);
      // Remove the selected word to avoid duplicates
      wordsCopy.removeAt(randomIndex);
    }

    return result;
  }

  void selectCategory() {
    try {
      final currentState = state.valueOrNull;
      if (currentState != null) {
        state = AsyncData(currentState.copyWith(gameStage: GameStage.topic));
      }
    } catch (error, stackTrace) {
      state = AsyncError('Failed to select category: $error', stackTrace);
    }
  }

  void selectTopic() {
    try {
      final currentState = state.valueOrNull;
      if (currentState != null) {
        state = AsyncData(currentState.copyWith(gameStage: GameStage.loading));
        viewTimerModel.setTotalSecond(seconds: 3);
        viewTimerModel.startTimer();
      }
    } catch (error, stackTrace) {
      state = AsyncError('Failed to select topic: $error', stackTrace);
    }
  }
}
