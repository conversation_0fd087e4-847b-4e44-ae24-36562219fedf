// observe_recall_screen.dart
import 'dart:ui';

import 'package:audioplayers/audioplayers.dart';
import 'package:cached_network_image/cached_network_image.dart'
    show CachedNetworkImageProvider;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/features/games/domain/models/game_word.dart';
import 'package:selfeng/features/games/presentation/providers/game_completion_controller.dart';
import 'package:selfeng/features/games/presentation/providers/memory_flash_controller.dart';
import 'package:selfeng/features/games/presentation/providers/state/memory_flash_state.dart';
import 'package:selfeng/features/games/presentation/widgets/loading_screen.dart';
import 'package:selfeng/services/timer_cache_service/presentation/providers/timer_controller.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class MemoryFlashScreen extends ConsumerStatefulWidget {
  const MemoryFlashScreen({super.key, required this.topic});
  final String topic;

  @override
  ConsumerState<MemoryFlashScreen> createState() => _MemoryFlashScreenState();
}

class _MemoryFlashScreenState extends ConsumerState<MemoryFlashScreen>
    with TickerProviderStateMixin {
  // State variables
  late AsyncValue<MemoryFlashState> viewState;
  late MemoryFlashController viewModel;
  late AsyncValue viewTimerState;
  late TimerController viewTimerModel;

  // Controllers and keys
  final _scaffoldKey = GlobalKey<ScaffoldState>();
  late final AudioPlayer _player = AudioPlayer();
  late AnimationController _wordController;

  // Flags and caches
  bool isInit = false;
  bool _isAudioPlayed = false;

  static const Duration _wordAnimationDuration = Duration(seconds: 5);
  static const Duration _slideAnimationDuration = Duration(seconds: 5);

  @override
  void initState() {
    super.initState();
    _initializeScreen();
    _setupAnimationController();
  }

  @override
  void dispose() {
    _cleanupResources();
    super.dispose();
  }

  // Initialization methods
  void _initializeScreen() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  void _setupAnimationController() {
    _wordController = AnimationController(
      duration: _wordAnimationDuration,
      vsync: this,
    );
    _wordController.addStatusListener(_handleAnimationStatus);
  }

  void _handleAnimationStatus(AnimationStatus status) {
    if (status == AnimationStatus.completed && mounted) {
      final currentMemoryFlashState =
          ref.read(memoryFlashControllerProvider(topic: widget.topic)).value;
      if (currentMemoryFlashState != null) {
        _wordController.reset();
        _wordController.forward();
      }
    }
  }

  void _cleanupResources() {
    _player.stop().then((_) => _player.dispose());
    _wordController.dispose();
    WakelockPlus.disable();
  }

  void init() {
    isInit = true;
  }

  @override
  Widget build(BuildContext context) {
    _initializeProviders();
    _setupListeners();
    WakelockPlus.enable();

    return _buildScreenContent();
  }

  void _initializeProviders() {
    final prov = memoryFlashControllerProvider(topic: widget.topic);
    viewState = ref.watch(prov);
    viewModel = ref.watch(prov.notifier);
    final provTimer = timerControllerProvider;
    viewTimerState = ref.watch(provTimer);
    viewTimerModel = ref.watch(provTimer.notifier);
  }

  void _setupListeners() {
    final prov = memoryFlashControllerProvider(topic: widget.topic);
    final provTimer = timerControllerProvider;

    ref.listen(prov.select((value) => value), _handleGameStateChange);
    ref.listen(provTimer.select((value) => value), _handleTimerStateChange);
  }

  void _handleGameStateChange(
    AsyncValue<MemoryFlashState>? previous,
    AsyncValue<MemoryFlashState> next,
  ) {
    next.maybeWhen(
      error: (error, track) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(error.toString())));
      },
      orElse: () {},
    );
  }

  void _handleTimerStateChange(AsyncValue? previous, AsyncValue next) {
    if (next.value?.totalSecond != 0) return;

    viewTimerModel.stopTimer();
    final currentGameStage = viewState.value?.gameStage;
    if (currentGameStage == null) return;

    _handleGameStageTimeout(currentGameStage);
  }

  void _handleGameStageTimeout(GameStage gameStage) {
    switch (gameStage) {
      case GameStage.topic:
      case GameStage.observe:
      case GameStage.result:
      case GameStage.finished:
        return;
      case GameStage.loading:
        viewModel.loadGameContent();
        return;
      case GameStage.recall:
        viewModel.gameOver();
        return;
      case GameStage.gameOver:
        viewModel.submitAnswers();
        return;
    }
  }

  Widget _buildScreenContent() {
    return switch (viewState) {
      AsyncData() => _shouldShowMainContent() ? _buildBody() : LoadingScreen(),
      AsyncError() => _buildBody(),
      AsyncLoading() => LoadingScreen(),
      _ => const Text('loading'),
    };
  }

  bool _shouldShowMainContent() {
    return viewState.value != null && viewState.value!.topic != null;
  }

  Widget _buildBody() {
    return Scaffold(key: _scaffoldKey, body: _buildBackgroundContainer());
  }

  Widget _buildBackgroundContainer() {
    return Container(
      decoration: _buildBackgroundDecoration(),
      child: BackdropFilter(
        filter: ImageFilter.blur(
          sigmaX: viewModel.isChooseGame ? 10 : 0,
          sigmaY: viewModel.isChooseGame ? 10 : 0,
        ),
        child: Container(
          color: Colors.grey.withValues(alpha: .1),
          alignment: Alignment.center,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            child: Stack(children: [_buildContent(), _buildTopBar()]),
          ),
        ),
      ),
    );
  }

  BoxDecoration _buildBackgroundDecoration() {
    return BoxDecoration(
      gradient: const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFFD84315), Color(0xFFFFA000)],
      ),
      image: DecorationImage(
        image: CachedNetworkImageProvider(viewState.value!.topic!.image ?? ''),
        fit: BoxFit.fill,
      ),
    );
  }

  Widget _buildTopBar() {
    return SafeArea(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (viewState.value!.gameStage != GameStage.finished)
            _buildBackButton(),
          if (viewState.value!.gameStage == GameStage.result)
            _buildNextButton(),
          if (viewModel.isTimer) _buildTimerSection(),
          if (viewState.value!.gameStage == GameStage.finished)
            _buildCloseButton(),
        ],
      ),
    );
  }

  Widget _buildBackButton() {
    if (viewState.value!.gameStage == GameStage.finished) {
      return Container();
    }
    return _buildImageButton(
      imagePath: '$assetImageGames/memory_flash/Navigate back Game.png',
      onTap: () {
        viewModel.exitGame();
        context.pop();
        // customNav(
        //   context,
        //   RouterName.topicGame,
        //   params: {"game": RouterName.memoryFlash},
        // );
      },
    );
  }

  Widget _buildNextButton() {
    if (viewState.value!.gameStage != GameStage.result) {
      return Container();
    }
    return _buildImageButton(
      imagePath: '$assetImageGames/memory_flash/Next page Game.png',
      onTap: () => viewModel.finishGame(),
    );
  }

  Widget _buildTimerSection() {
    if (!viewModel.isTimer) return Container();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          _buildImageButton(
            imagePath: _getTimerIconPath(),
            onTap: () => _showInstructionDialog(),
            size: 40,
          ),
          const SizedBox(width: 4),
          _buildTimerText(),
        ],
      ),
    );
  }

  String _getTimerIconPath() {
    final isRecallStage = viewState.value!.gameStage == GameStage.recall;
    return '$assetImageGames/memory_flash/${isRecallStage ? 'Shines Info-Game4' : 'Timer-Game4'}.png';
  }

  Widget _buildTimerText() {
    final minutes = viewTimerModel.getMinute().toString().padLeft(2, '0');
    final seconds = viewTimerModel.getSecond().toString().padLeft(2, '0');

    return Text(
      '$minutes:$seconds',
      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
        color: Colors.white,
        fontWeight: FontWeight.w800,
      ),
    );
  }

  Widget _buildCloseButton() {
    if (viewState.value!.gameStage != GameStage.finished) {
      return Container();
    }
    return _buildImageButton(
      imagePath: '$assetImageGames/memory_flash/Close page scores Game.png',
      onTap: () {
        viewModel.exitGame();
        context.pop();
      },
    );
  }

  Widget _buildImageButton({
    required String imagePath,
    required VoidCallback onTap,
    double size = 30,
  }) {
    return InkWell(
      onTap: onTap,
      child: Image.asset(imagePath, height: size, width: size),
    );
  }

  void _showInstructionDialog() {
    showDialog<String>(
      context: context,
      builder: (BuildContext context) => _buildInstruction(),
    );
  }

  Widget _buildInstruction() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16, horizontal: 80),
      padding: const EdgeInsets.symmetric(vertical: 46, horizontal: 42),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: const LinearGradient(
          begin: Alignment.centerRight,
          end: Alignment.centerLeft,
          colors: [Color(0xff8B000A), Color(0xffF33928), Color(0xffFF8D46)],
        ),
      ),
      child: Material(
        type: MaterialType.transparency,
        child: Column(
          children: [_buildInstructionHeader(), _buildInstructionContent()],
        ),
      ),
    );
  }

  Widget _buildInstructionHeader() {
    return Align(
      alignment: Alignment.topRight,
      child: InkWell(
        onTap: () => context.pop(),
        child: Image.asset(
          '$assetImageGames/memory_flash/Close page scores Game.png',
          height: 40,
          width: 40,
        ),
      ),
    );
  }

  Widget _buildInstructionContent() {
    return Column(
      children: [
        Text(
          context.loc.gameplay,
          style: Theme.of(
            context,
          ).textTheme.headlineLarge!.copyWith(color: Colors.white),
          textScaler: const TextScaler.linear(0.8),
        ),
        const SizedBox(height: 16),
        Text(
          context.loc.how_to_play_memory_flash,
          style: Theme.of(
            context,
          ).textTheme.headlineSmall!.copyWith(color: Colors.white),
          textScaler: const TextScaler.linear(0.8),
        ),
      ],
    );
  }

  Widget _buildContent() {
    if (viewState.value == null) {
      return const Center(child: CircularProgressIndicator());
    }

    final currentGameStage = viewState.value?.gameStage;
    if (currentGameStage == null) {
      return LoadingScreen();
    }

    return switch (currentGameStage) {
      GameStage.topic => _buildLoadingScreenStage(),
      GameStage.loading => _buildLoadingScreenStage(),
      GameStage.observe => _buildObserveStage(),
      GameStage.recall => SafeArea(child: _buildRecallStage()),
      GameStage.result => SafeArea(child: _buildResultStage()),
      GameStage.finished => _buildFinishedStage(),
      GameStage.gameOver => _buildGameOver(),
    };
  }

  Widget _buildLoadingScreenStage() {
    final timerValue = viewTimerState.value?.totalSecond;
    final image = _getLoadingStageImage(timerValue);
    final height = _getLoadingStageHeight(timerValue);

    if (timerValue == 2) {
      _playAudio('sounds/memory_flash/Ready Go.MP3');
    }

    return Center(child: _buildLoadingImage(image, height));
  }

  String _getLoadingStageImage(int? timerValue) {
    if (timerValue == null) return '$assetImageGames/memory_flash/Ready.png';

    return switch (timerValue) {
      0 || 1 => '$assetImageGames/memory_flash/Go!.png',
      3 => _getTopicIconSafely(),
      _ => '$assetImageGames/memory_flash/Ready.png',
    };
  }

  String _getTopicIconSafely() {
    try {
      final topic = viewState.valueOrNull?.topic;
      return topic?.icon ?? '$assetImageGames/memory_flash/Ready.png';
    } catch (e) {
      debugPrint('Error getting topic icon: $e');
      return '$assetImageGames/memory_flash/Ready.png';
    }
  }

  bool _isNetworkImage(String imagePath) {
    return imagePath.startsWith('http://') || imagePath.startsWith('https://');
  }

  Widget _buildLoadingImage(String imagePath, double height) {
    try {
      if (_isNetworkImage(imagePath)) {
        // Handle network images
        return Image.network(
          imagePath,
          height: height,
          fit: BoxFit.fitHeight,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return SizedBox(
              height: height,
              child: Center(
                child: CircularProgressIndicator(
                  value:
                      loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                ),
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            debugPrint(
              'Failed to load network image: $imagePath, Error: $error',
            );
            // Fallback to default local asset
            return Image.asset(
              '$assetImageGames/memory_flash/Ready.png',
              height: height,
              fit: BoxFit.fitHeight,
              errorBuilder: (context, error, stackTrace) {
                debugPrint('Failed to load fallback asset image: $error');
                return Container(
                  height: height,
                  color: Colors.grey[300],
                  child: const Icon(
                    Icons.image_not_supported,
                    color: Colors.grey,
                  ),
                );
              },
            );
          },
        );
      } else {
        // Handle local assets
        return Image.asset(
          imagePath,
          height: height,
          fit: BoxFit.fitHeight,
          errorBuilder: (context, error, stackTrace) {
            debugPrint('Failed to load asset image: $imagePath, Error: $error');
            return Container(
              height: height,
              color: Colors.grey[300],
              child: const Icon(Icons.image_not_supported, color: Colors.grey),
            );
          },
        );
      }
    } catch (e, stackTrace) {
      debugPrint('Exception in _buildLoadingImage: $e');
      debugPrint('StackTrace: $stackTrace');
      // Return a safe fallback widget
      return Container(
        height: height,
        color: Colors.grey[300],
        child: const Icon(Icons.error, color: Colors.red),
      );
    }
  }

  double _getLoadingStageHeight(int? timerValue) {
    return (timerValue != null && timerValue > 2) ? 200 : 94;
  }

  Widget _buildObserveStage() {
    final currentState = viewState.valueOrNull;
    if (currentState == null) {
      return const Center(child: CircularProgressIndicator());
    }

    _handleObserveStageAudio();

    return Column(
      children: [
        const SizedBox(height: 15),
        _buildStageTitle(context.loc.remember_7_items),
        Expanded(
          child: RepaintBoundary(
            child: Stack(
              children:
                  viewState.value!.activeWords.map(_buildAnimatedWord).toList(),
            ),
          ),
        ),
      ],
    );
  }

  void _handleObserveStageAudio() {
    if (!_isAudioPlayed) {
      _isAudioPlayed = true;
      _playAudio(
        'sounds/memory_flash/Topic ${viewState.value!.topic!.title!}.MP3',
      );
      _wordController.forward();
    } else {
      switch (viewState.value!.activeWords.length) {
        case 5:
          _player.setVolume(0.4);
          break;
        case 6:
          _player.setVolume(0.3);
          break;
        case 7:
          _player.setVolume(0.1);
          break;
      }
    }
  }

  Widget _buildStageTitle(String title) {
    return Text(
      title,
      style: Theme.of(
        context,
      ).textTheme.headlineLarge!.copyWith(color: Colors.white),
      textScaler: const TextScaler.linear(0.8),
    );
  }

  Widget _buildAnimatedWord(WordObject wordObj) {
    return TweenAnimationBuilder<double>(
      duration: _slideAnimationDuration,
      tween: Tween<double>(begin: -0.2, end: 1.2),
      builder: (context, slideValue, child) {
        return Positioned(
          left: MediaQuery.of(context).size.width * slideValue,
          top:
              MediaQuery.of(context).size.height *
              wordObj.verticalPosition! *
              0.35,
          child: _buildWordContainer(wordObj.word),
        );
      },
    );
  }

  Widget _buildWordContainer(String word) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xffFFDAD6),
        borderRadius: BorderRadius.circular(5),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: .3),
            blurRadius: 4,
            offset: const Offset(2, 2),
          ),
        ],
      ),
      child: Text(
        word,
        style: Theme.of(
          context,
        ).textTheme.headlineSmall!.copyWith(color: const Color(0xffAE202E)),
      ),
    );
  }

  Widget _buildRecallStage() {
    if (_isAudioPlayed) {
      _player.stop();
      _isAudioPlayed = false;
    }
    return Column(
      children: [
        const SizedBox(height: 15),
        _buildStageTitle(context.loc.tap_items_you_saw),
        const SizedBox(height: 20),
        _buildWordChoices(),
      ],
    );
  }

  Widget _buildWordChoices() {
    return Wrap(
      spacing: 10,
      runSpacing: 10,
      children: viewState.value!.words.map(_buildWordChoice).toList(),
    );
  }

  Widget _buildWordChoice(String word) {
    final isSelected = viewState.value!.wordChoices.contains(word);

    return ElevatedButton(
      onPressed: () => _handleWordSelection(word),
      style: _getWordChoiceStyle(isSelected),
      child: Text(
        word,
        style: _getWordChoiceTextStyle(isSelected),
        textScaler: const TextScaler.linear(0.8),
      ),
    );
  }

  void _handleWordSelection(String word) {
    viewModel.selectWord(word);
    _playAudio('sounds/memory_flash/Pilih Kata.MP3');
  }

  ButtonStyle _getWordChoiceStyle(bool isSelected) {
    return ElevatedButton.styleFrom(
      backgroundColor:
          isSelected ? const Color(0xffE41A19) : const Color(0xffFFDAD6),
      minimumSize: const Size(120, 50),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
        side: const BorderSide(color: Colors.white),
      ),
    );
  }

  TextStyle? _getWordChoiceTextStyle(bool isSelected) {
    return Theme.of(context).textTheme.headlineSmall?.copyWith(
      color: isSelected ? Colors.white : const Color(0xffAE202E),
    );
  }

  Widget _buildResultStage() {
    _playAudio("sounds/memory_flash/score.mp3");
    return Column(
      children: [
        const SizedBox(height: 15),
        _buildStageTitle(context.loc.memory_flash_result_desc),
        const SizedBox(height: 20),
        _buildResultWords(),
      ],
    );
  }

  Widget _buildResultWords() {
    return Wrap(
      spacing: 10,
      runSpacing: 10,
      children: viewState.value!.words.map(_buildResultWord).toList(),
    );
  }

  Widget _buildResultWord(String word) {
    final isSelected = viewState.value!.wordChoices.contains(word);

    return Stack(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildResultWordButton(word, isSelected),
            if (isSelected) const SizedBox(width: 25),
          ],
        ),
        if (isSelected) _buildResultIcon(word),
      ],
    );
  }

  Widget _buildResultWordButton(String word, bool isSelected) {
    return ElevatedButton(
      onPressed: () {},
      style: _getWordChoiceStyle(isSelected),
      child: Text(
        word,
        style: _getWordChoiceTextStyle(isSelected),
        textScaler: const TextScaler.linear(0.8),
      ),
    );
  }

  Widget _buildResultIcon(String word) {
    final isCorrect = viewState.value!.wordRandom.contains(word);
    return Positioned(
      right: 0,
      child: Image.asset(
        '$assetImageGames/memory_flash/${isCorrect ? 'True' : 'False'} - Game.png',
        height: 50,
        width: 50,
      ),
    );
  }

  Widget _buildFinishedStage() {
    _playAudio('sounds/memory_flash/Hasil Score Keluar.MP3');

    return ListView(
      children: [
        const SizedBox(height: 40),
        _buildScoreContainer(),
        const SizedBox(height: 18),
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildScoreContainer() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(left: 16, right: 16, bottom: 20),
      padding: const EdgeInsets.symmetric(vertical: 46, horizontal: 42),
      decoration: _getScoreContainerDecoration(),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildScoreSection(
            context.loc.topic_complete,
            context.loc.congratulations,
          ),
          _buildScoreSection(context.loc.level_complete, _getScoreText()),
        ],
      ),
    );
  }

  BoxDecoration _getScoreContainerDecoration() {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(16),
      gradient: const LinearGradient(
        begin: Alignment.centerRight,
        end: Alignment.centerLeft,
        colors: [Color(0xff8B000A), Color(0xffF33928), Color(0xffFF8D46)],
      ),
    );
  }

  Widget _buildScoreSection(String title, String subtitle) {
    return Column(
      children: [
        Text(
          title,
          style: Theme.of(
            context,
          ).textTheme.headlineLarge?.copyWith(color: Colors.white),
          textScaler: const TextScaler.linear(0.8),
        ),
        _buildStarRating(),
        _buildScoreLabel(subtitle),
      ],
    );
  }

  Widget _buildStarRating() {
    final starCount = (viewState.value!.score / 7 * 4).round();
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 14),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(starCount, (index) {
          return Image.asset(
            '$assetImageGames/memory_flash/Star2-Game.png',
            height: 50,
            width: 50,
          );
        }),
      ),
    );
  }

  Widget _buildScoreLabel(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 10),
      decoration: _getScoreLabelDecoration(),
      child: Text(
        text,
        style: Theme.of(
          context,
        ).textTheme.titleMedium?.copyWith(color: Colors.white),
        textScaler: const TextScaler.linear(0.8),
      ),
    );
  }

  BoxDecoration _getScoreLabelDecoration() {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(20),
      gradient: const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xffFA3236), Color(0xffD9353C), Color(0xff8C1412)],
      ),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: .3),
          blurRadius: 3,
          offset: const Offset(2, 3),
        ),
      ],
    );
  }

  String _getScoreText() {
    return '${context.loc.score}: ${viewState.value!.score}/${viewState.value!.wordRandom.length}  🤩✨';
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildActionButton(context.loc.replay, _handleReplay),
        const SizedBox(width: 84),
        _buildActionButton(context.loc.next, _handleNext),
      ],
    );
  }

  Widget _buildActionButton(String text, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 10),
        decoration: _getActionButtonDecoration(),
        child: Text(
          text,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(color: Colors.white),
        ),
      ),
    );
  }

  BoxDecoration _getActionButtonDecoration() {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(4),
      gradient: const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Color(0xffF36341), Color(0xffC70039)],
      ),
    );
  }

  void _handleReplay() {
    _isAudioPlayed = false;
    viewModel.resetGame();
  }

  void _handleNext() {
    // Mark game as completed using the game completion controller
    ref.read(gameCompletionControllerProvider.notifier).markGameCompleted();
    // Navigate back without returning a value
    context.pop();
  }

  Widget _buildGameOver() {
    _playAudio('sounds/memory_flash/Game Over.MP3');
    return Center(
      child: Image.asset(
        '$assetImageGames/memory_flash/Game over.png',
        height: MediaQuery.of(context).size.height * 0.88,
      ),
    );
  }

  // Utility methods
  void _playAudio(String path) async {
    await _player.stop();
    _player.play(AssetSource(path));
  }
}
