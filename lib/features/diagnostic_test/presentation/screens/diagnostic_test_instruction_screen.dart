import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/diagnostic_test/presentation/providers/diagnostic_test_controller.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';

class DiagnosticTestInstructionScreen extends ConsumerStatefulWidget {
  const DiagnosticTestInstructionScreen({super.key});

  @override
  ConsumerState<DiagnosticTestInstructionScreen> createState() =>
      _DiagnosticTestInstructionScreenState();
}

class _DiagnosticTestInstructionScreenState
    extends ConsumerState<DiagnosticTestInstructionScreen> {
  late AsyncValue viewState;
  late DiagnosticTestController viewModel;

  @override
  Widget build(BuildContext context) {
    viewState = ref.watch(diagnosticTestControllerProvider);
    viewModel = ref.watch(diagnosticTestControllerProvider.notifier);

    ref.listen(diagnosticTestControllerProvider.select((value) => value), ((
      previous,
      next,
    ) {
      //show Snackbar on failure
      next.maybeWhen(
        error: (error, track) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(error.toString())));
        },
        orElse: () {},
      );
    }));
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xffFFF2F2),
              Color(0xffFDD8D8),
              Color(0xffFFFFFF),
              Color(0xffFFFFFF),
            ],
            begin: Alignment.bottomLeft,
            end: Alignment.topRight,
          ),
        ),
        child: Stack(
          children: [
            Align(
              alignment: FractionalOffset.bottomRight,
              child: Image.asset('$assetImageDiagnosticTest/PICT37.png'),
            ),
            Align(
              alignment: FractionalOffset.bottomLeft,
              child: Image.asset('$assetImageDiagnosticTest/PICT36.png'),
            ),
            ListView(
              children: [
                Text(
                  textAlign: TextAlign.center,
                  context.loc.testInstruction,
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 24),
                Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Color(0xffFA3236),
                        Color(0xffD9353C),
                        Color(0xff8C1412),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  padding: const EdgeInsets.symmetric(
                    vertical: 14.5,
                    horizontal: 10,
                  ),
                  child: Text(
                    // textAlign: TextAlign.center,
                    context.loc.testInstructionDesc,
                    style: Theme.of(
                      context,
                    ).textTheme.titleMedium?.copyWith(color: Colors.white),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: 24,
                    horizontal: 16,
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Image.asset(
                            '$assetImageDiagnosticTest/Help.png',
                            fit: BoxFit.fitWidth,
                            width: 20,
                            height: 32,
                          ),
                          const SizedBox(width: 12),
                          SizedBox(
                            width: MediaQuery.of(context).size.width - 149,
                            child: Text(
                              context.loc.instruction,
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      Text(
                        context.loc.testInstructionRule,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(color: const Color(0xff7F7574)),
                      ),
                      const SizedBox(height: 40),
                      SizedBox(
                        // bottom: 72,
                        child: VButtonGradient(
                          title: context.loc.doIt,
                          onTap: () {
                            customNav(
                              context,
                              RouterName.diagnosticTestScreen,
                              isReplace: true,
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            // Positioned(
            //   bottom: 72,
            //   child: VButtonGradient(
            //     title: context.loc.doIt,
            //     onTap: () {
            //       customNav(
            //         context,
            //         RouterName.diagnosticTestScreen,
            //         isReplace: true,
            //       );
            //     },
            //   ),
            // ),
          ],
        ),
      ),
    );
  }
}
