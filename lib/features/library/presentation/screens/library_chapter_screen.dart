import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/features/library/presentation/providers/library_chapter_controller.dart';
import 'package:selfeng/features/library/presentation/widgets/level_card.dart';
import 'package:selfeng/features/library/presentation/widgets/library_chapter_card.dart';
import 'package:selfeng/shared/domain/models/level.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';

class LibraryChapterScreen extends ConsumerStatefulWidget {
  final String level;

  const LibraryChapterScreen({super.key, required this.level});

  @override
  ConsumerState<LibraryChapterScreen> createState() =>
      _LibraryChapterScreenState();
}

class _LibraryChapterScreenState extends ConsumerState<LibraryChapterScreen> {
  late final Level _levelEnum = Level.values.byName(widget.level.toLowerCase());
  late final _provider = libraryChapterControllerProvider(widget.level);

  @override
  Widget build(BuildContext context) {
    final libraryChapterState = ref.watch(_provider);

    return Scaffold(
      backgroundColor: Colors.white,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xffFFFFFF),
              Color(0xffFFECEC),
              Color(0xffFDD8D8),
              Color(0xffFFF2F2),
            ],
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
          ),
        ),
        child: libraryChapterState.when(
          data:
              (state) => Column(
                children: [
                  SizedBox(height: MediaQuery.of(context).padding.top),
                  Stack(
                    children: [
                      if (state.levelInfo != null)
                        LevelCard(data: state.levelInfo!, tapable: false),
                      Positioned(
                        top: 10,
                        left: 10,
                        child: GestureDetector(
                          onTap: () => context.pop(),
                          child: Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Color(0xffFFB3AC).withValues(alpha: .8),
                              border: Border.all(color: Colors.white, width: 1),
                            ),
                            height: 40,
                            width: 40,
                            child: const Icon(
                              Icons.chevron_left_rounded,
                              size: 36,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  Expanded(
                    child:
                        state.chapters.isEmpty
                            ? const LoadingCircle()
                            : ListView.builder(
                              padding: EdgeInsets.zero,
                              itemCount: state.chapters.length,
                              itemBuilder:
                                  (context, index) => LibraryChapterCard(
                                    chapter: state.chapters[index],
                                    level: _levelEnum,
                                  ),
                            ),
                  ),
                ],
              ),
          loading:
              () => Column(
                children: [
                  const SizedBox(height: 80),
                  // Show a shimmer effect for the level card
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    height: 120,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: .7),
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  const Expanded(child: Center(child: LoadingCircle())),
                ],
              ),
          error: (error, stack) => Center(child: Text(error.toString())),
        ),
      ),
    );
  }
}
