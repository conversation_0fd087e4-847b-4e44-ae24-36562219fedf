import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/features/library/domain/models/content_info.dart';
import 'package:selfeng/features/library/presentation/providers/chapter_content_provider.dart';
import 'package:selfeng/features/library/presentation/providers/library_chapter_content_controller.dart';
import 'package:selfeng/features/library/presentation/providers/state/chapter_content_state.dart';
import 'package:selfeng/features/library/presentation/providers/state/library_chapter_content_state.dart';
import 'package:selfeng/features/library/presentation/widgets/library_chapter_card.dart';
import 'package:selfeng/features/library/presentation/widgets/content_section_card.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/shared/domain/models/level.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';

class LibraryChapterContentScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;

  const LibraryChapterContentScreen({
    super.key,
    required this.level,
    required this.chapter,
  });

  @override
  ConsumerState<LibraryChapterContentScreen> createState() =>
      _LibraryChapterContentState();
}

class _LibraryChapterContentState
    extends ConsumerState<LibraryChapterContentScreen> {
  List<ContentInfo> _contentSections = [];
  bool _isInitialized = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isInitialized) {
      _initializeContentSections();
    }
  }

  void _initializeContentSections() {
    _contentSections = [
      ContentInfo(
        title: context.loc.pronunciationChallenge,
        image: '$assetImageMainLesson/pronunciationchallenge.png',
        icon: '$assetImageMainLesson/iconpronunciation.png',
        section: SectionType.pronunciation,
      ),
      ContentInfo(
        title: context.loc.conversationVideo,
        image: '$assetImageMainLesson/conversationvideo.png',
        icon: '$assetImageMainLesson/iconconversation.png',
        section: SectionType.conversation,
      ),
      ContentInfo(
        title: context.loc.listeningMastery,
        image: '$assetImageMainLesson/listeningmastery.png',
        icon: '$assetImageMainLesson/iconlistening.png',
        section: SectionType.listening,
      ),
      ContentInfo(
        title: context.loc.speakingArena,
        image: '$assetImageMainLesson/speakingarena.png',
        icon: '$assetImageMainLesson/iconspeaking.png',
        section: SectionType.speaking,
      ),
    ];

    _isInitialized = true;
  }

  @override
  Widget build(BuildContext context) {
    final contentProvider = libraryChapterContentControllerProvider(
      widget.level,
      widget.chapter,
    );
    final contentState = ref.watch(contentProvider);

    final ChapterContentState content = ref.watch(chapterContentStateProvider);

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xffFFFFFF),
              Color(0xffFFECEC),
              Color(0xffFDD8D8),
              Color(0xffFFF2F2),
            ],
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
          ),
        ),
        child: contentState.when(
          data: (state) => _buildContent(state, content),
          loading: () => const Center(child: LoadingCircle()),
          error: (error, stackTrace) => Center(child: Text(error.toString())),
        ),
      ),
    );
  }

  Widget _buildContent(
    LibraryChapterContentState state,
    ChapterContentState content,
  ) {
    return Column(
      children: [
        SizedBox(height: MediaQuery.of(context).padding.top),
        _buildChapterHeader(state),
        Expanded(
          child:
              _contentSections.isEmpty
                  ? const Center(child: Text("No items available"))
                  : _buildContentSectionsList(state, content),
        ),
      ],
    );
  }

  Widget _buildChapterHeader(LibraryChapterContentState state) {
    if (state.chapter == null) {
      return const LoadingCircle();
    }

    return Stack(
      children: [
        LibraryChapterCard(
          chapter: state.chapter!,
          level: Level.values.byName(widget.level.toLowerCase()),
          tapable: false,
        ),
        Positioned(top: 10, left: 10, child: _buildBackButton()),
      ],
    );
  }

  Widget _buildBackButton() {
    return GestureDetector(
      onTap: () => context.pop(),
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: const Color(0xffFFB3AC).withValues(alpha: 0.2),
          border: Border.all(color: Colors.white, width: 1),
        ),
        height: 40,
        width: 40,
        child: const Icon(
          Icons.chevron_left_rounded,
          size: 36,
          color: Colors.black,
        ),
      ),
    );
  }

  Widget _buildContentSectionsList(
    LibraryChapterContentState state,
    ChapterContentState content,
  ) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _contentSections.length,
      itemBuilder: (context, index) {
        final section = _contentSections[index];
        List<ContentIndexData>? sectionContent;

        switch (section.section) {
          case SectionType.pronunciation:
            sectionContent = content.pronunciationContent;
            break;
          case SectionType.conversation:
            sectionContent = content.conversationContent;
            break;
          case SectionType.listening:
            sectionContent = content.listeningContent;
            break;
          case SectionType.speaking:
            sectionContent = content.speakingContent;
            break;
        }

        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: ContentSectionCard(
            contentInfo: section,
            content: sectionContent,
            level: widget.level,
            chapter: widget.chapter,
          ),
        );
      },
    );
  }
}
