import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
// Assuming SpeakingStage enum is available, possibly from main_lesson.dart or another import
// For example: import 'package:selfeng/features/main_lesson/domain/models/speaking_stage.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';

class ContentTree extends StatelessWidget {
  final List<ContentIndexData> content;
  final String iconPath;
  final String level;
  final String chapter;
  final String? section;

  const ContentTree({
    super.key,
    required this.content,
    required this.iconPath,
    required this.level,
    required this.chapter,
    this.section,
  });

  @override
  Widget build(BuildContext context) {
    if (section == 'speaking') {
      return Column(
        children: [
          StyledExpansionTile(
            title: 'Stage 1',
            leadingIcon: Icons.folder_rounded,
            iconColor: const Color(0xFFD60012),
            textStyle: TextStyle(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
              fontSize: 16,
            ),
            children:
                content
                    .map(
                      (item) => ContentTreeItem(
                        item: item,
                        iconPath: iconPath,
                        level: level,
                        chapter: chapter,
                        path: item.contentPath,
                        section: section,
                        stage: SpeakingStage.stage1,
                      ),
                    )
                    .toList(),
          ),
          StyledExpansionTile(
            title: 'Stage 2',
            leadingIcon: Icons.folder_rounded,
            iconColor: const Color(0xFFD60012),
            textStyle: TextStyle(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
              fontSize: 16,
            ),
            children:
                content
                    .map(
                      (item) => ContentTreeItem(
                        item: item,
                        iconPath: iconPath,
                        level: level,
                        chapter: chapter,
                        path: item.contentPath,
                        section: section,
                        stage: SpeakingStage.stage2,
                      ),
                    )
                    .toList(),
          ),
          StyledExpansionTile(
            title: 'Stage 3',
            leadingIcon: Icons.folder_rounded,
            iconColor: const Color(0xFFD60012),
            textStyle: TextStyle(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
              fontSize: 16,
            ),
            children:
                content
                    .map(
                      (item) => ContentTreeItem(
                        item: item,
                        iconPath: iconPath,
                        level: level,
                        chapter: chapter,
                        path: item.contentPath,
                        section: section,
                        stage: SpeakingStage.stage3,
                      ),
                    )
                    .toList(),
          ),
        ],
      );
    }

    // Separate content into items with partTitle and those without
    final itemsWithPart = <ContentIndexData>[];
    final itemsWithoutPart = <ContentIndexData>[];

    for (final item in content) {
      if (item.partTitle == null || item.partTitle!.isEmpty) {
        itemsWithoutPart.add(item);
      } else {
        itemsWithPart.add(item);
      }
    }

    return Column(
      children: [
        // Render items without partTitle directly in the root
        ...itemsWithoutPart.map(
          (item) => ContentTreeItem(
            item: item,
            iconPath: iconPath,
            level: level,
            chapter: chapter,
            section: section,
            path: item.contentPath,
          ),
        ),

        // Only process grouped items if there are any
        if (itemsWithPart.isNotEmpty)
          _buildGroupedContent(itemsWithPart, context),
      ],
    );
  }

  Widget _buildGroupedContent(
    List<ContentIndexData> groupedItems,
    BuildContext context,
  ) {
    // Group by part with null safety
    final parts = <String, List<ContentIndexData>>{};
    for (final item in groupedItems) {
      final partKey = item.partTitle ?? '';
      parts.putIfAbsent(partKey, () => []).add(item);
    }

    return Column(
      children:
          parts.entries.map((partEntry) {
            // Group subparts using subpartTitle with null safety
            final subparts = <String, List<ContentIndexData>>{};
            final itemsWithoutSubpart = <ContentIndexData>[];

            for (final item in partEntry.value) {
              if (item.subpartTitle == null || item.subpartTitle!.isEmpty) {
                itemsWithoutSubpart.add(item);
              } else {
                final subpartKey = item.subpartTitle!;
                subparts.putIfAbsent(subpartKey, () => []).add(item);
              }
            }

            // If no subparts, just show part items
            if (subparts.isEmpty) {
              return StyledExpansionTile(
                title: partEntry.key,
                leadingIcon: Icons.folder_rounded,
                iconColor: const Color(0xFFD60012),
                textStyle: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                  fontSize: 16,
                ),
                children:
                    partEntry.value
                        .map(
                          (item) => ContentTreeItem(
                            item: item,
                            iconPath: iconPath,
                            level: level,
                            chapter: chapter,
                            path: item.contentPath,
                            section: section,
                          ),
                        )
                        .toList(),
              );
            }

            // Show part with subparts
            return StyledExpansionTile(
              title: partEntry.key,
              leadingIcon: Icons.folder_rounded,
              iconColor: const Color(0xFFD60012),
              textStyle: TextStyle(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
                fontSize: 16,
              ),
              children: [
                // First show items without subpart
                ...itemsWithoutSubpart.map(
                  (item) => Padding(
                    padding: const EdgeInsets.only(left: 16),
                    child: ContentTreeItem(
                      item: item,
                      iconPath: iconPath,
                      level: level,
                      chapter: chapter,
                      path: item.contentPath,
                      section: section,
                    ),
                  ),
                ),

                // Then show subparts
                ...subparts.entries.map((subEntry) {
                  return Padding(
                    padding: const EdgeInsets.only(left: 16),
                    child: StyledExpansionTile(
                      title: subEntry.key,
                      leadingIcon: Icons.subdirectory_arrow_right_rounded,
                      iconColor: const Color(0xFFF96337),
                      textStyle: const TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF555555),
                        fontSize: 15,
                      ),
                      borderColor: const Color(0xFFFFECEC),
                      children:
                          subEntry.value
                              .map(
                                (item) => ContentTreeItem(
                                  item: item,
                                  iconPath: iconPath,
                                  level: level,
                                  chapter: chapter,
                                  path: item.contentPath,
                                  section: section,
                                ),
                              )
                              .toList(),
                    ),
                  );
                }),
              ],
            );
          }).toList(),
    );
  }
}

class StyledExpansionTile extends StatelessWidget {
  final String title;
  final List<Widget> children;
  final IconData leadingIcon;
  final Color iconColor;
  final TextStyle textStyle;
  final Color? borderColor;

  const StyledExpansionTile({
    super.key,
    required this.title,
    required this.children,
    required this.leadingIcon,
    required this.iconColor,
    required this.textStyle,
    this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border:
              borderColor != null
                  ? Border.all(color: borderColor!, width: 1)
                  : null,
          boxShadow:
              borderColor == null
                  ? [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ]
                  : null,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: ExpansionTile(
            tilePadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 4,
            ),
            childrenPadding: const EdgeInsets.only(bottom: 8),
            expandedAlignment: Alignment.topLeft,
            leading: Icon(leadingIcon, color: iconColor, size: 22),
            title: Text(title, style: textStyle),
            iconColor: iconColor,
            collapsedIconColor: iconColor,
            children: children,
          ),
        ),
      ),
    );
  }
}

class ContentTreeItem extends StatelessWidget {
  final ContentIndexData item;
  final String iconPath;
  final String level;
  final String chapter;
  final String? section;
  final String path;
  final SpeakingStage? stage;

  const ContentTreeItem({
    super.key,
    required this.item,
    required this.iconPath,
    required this.level,
    required this.chapter,
    required this.path,
    this.section,
    this.stage,
  });

  // Helper method to determine the trailing icon
  Widget _buildTrailingIcon() {
    bool showGreenCheck = false;

    if (section == 'speaking') {
      // Logic for 'speaking' section
      // Assumes item has firtStage, secondStage, thirdStage boolean properties
      // Using "firtStage" as per the prompt
      if (stage == SpeakingStage.stage1 && item.firstStage == true) {
        showGreenCheck = true;
      } else if (stage == SpeakingStage.stage2 && item.secondStage == true) {
        showGreenCheck = true;
      } else if (stage == SpeakingStage.stage3 && item.thirdStage == true) {
        showGreenCheck = true;
      }
      // If none of the above conditions are met, showGreenCheck remains false,
      // leading to the chevron icon.
    } else {
      // Original logic for non-'speaking' sections
      showGreenCheck = item.hasResult;
    }

    if (showGreenCheck) {
      return const Icon(Icons.check_circle, size: 18, color: Colors.green);
    } else {
      return const Icon(
        Icons.chevron_right_rounded,
        size: 18,
        color: Color(0xFFAAAAAA),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final params = <String, String>{
      'level': level,
      'chapter': chapter,
      'path': base64Url.encode(utf8.encode(path)),
    };
    String route = '';
    switch (section) {
      case 'pronunciation':
        route = RouterName.pronunciationChallenge;
        break;
      case 'conversation':
        route = RouterName.conversationVideo;
        break;
      case 'listening':
        route = RouterName.listeningMastery;
        break;
      case 'speaking':
        if (stage == SpeakingStage.stage1) {
          route = RouterName.speakingArena;
        } else {
          route = RouterName.speakingArenaStage;
          params['stage'] =
              stage?.name ??
              'stage2'; // Default to stage2 if stage is somehow null, though ContentTree ensures it's set
        }
        break;
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => customNav(context, route, params: params),
          splashColor: Theme.of(context).primaryColor.withValues(alpha: .1),
          highlightColor: Theme.of(context).primaryColor.withValues(alpha: .05),
          borderRadius: BorderRadius.circular(6),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
            child: Row(
              children: [
                Container(
                  width: 28,
                  height: 28,
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFFECEC),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Image.asset(
                    iconPath,
                    width: 24,
                    height: 24,
                    errorBuilder: (context, error, stackTrace) {
                      return const Icon(
                        Icons.description_outlined,
                        size: 20,
                        color: Color(0xFFD60012),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    item.title,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF333333),
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
                // Use the helper method to build the trailing icon
                _buildTrailingIcon(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
