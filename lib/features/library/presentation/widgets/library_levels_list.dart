import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/features/library/presentation/providers/library_controller.dart';
import 'package:selfeng/features/library/presentation/widgets/level_card.dart';

class LibraryLevelsList extends ConsumerWidget {
  const LibraryLevelsList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final levels = ref.watch(
      libraryControllerProvider.select((value) => value.value?.levels),
    );

    return ListView.builder(
      padding: const EdgeInsets.fromLTRB(0, 10, 0, 0),
      itemCount: levels?.length ?? 0,
      itemBuilder: (context, index) {
        return LevelCard(data: levels![index]);
      },
    );
  }
}
