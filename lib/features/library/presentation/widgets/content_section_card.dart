import 'package:flutter/material.dart';
import 'package:selfeng/features/library/domain/models/content_info.dart';
import 'package:selfeng/features/library/presentation/widgets/content_tree.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';

class ContentSectionCard extends StatefulWidget {
  final ContentInfo contentInfo;
  final List<ContentIndexData>? content;
  final String level;
  final String chapter;

  const ContentSectionCard({
    super.key,
    required this.contentInfo,
    required this.content,
    required this.level,
    required this.chapter,
  });

  @override
  State<ContentSectionCard> createState() => _ContentSectionCardState();
}

class _ContentSectionCardState extends State<ContentSectionCard> {
  bool _isExpanded = false;

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: .05),
            spreadRadius: 1,
            blurRadius: 5,
          ),
        ],
      ),
      child: Column(
        children: [_buildHeader(), if (_isExpanded) _buildExpandedContent()],
      ),
    );
  }

  Widget _buildHeader() {
    return GestureDetector(
      onTap: _toggleExpansion,
      child: Container(
        height: 80,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            stops: [0.1498, 0.5133, 0.797, 0.9056],
            colors: [
              Color(0xFF5F0007),
              Color(0xFFD60012),
              Color(0xFFF96337),
              Color(0xFFFF8D46),
            ],
          ),
          borderRadius: BorderRadius.vertical(
            top: const Radius.circular(12),
            bottom: _isExpanded ? Radius.zero : const Radius.circular(12),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    Image.asset(
                      widget.contentInfo.image,
                      errorBuilder: (context, error, stackTrace) {
                        return const Icon(
                          Icons.image_not_supported,
                          color: Colors.white,
                        );
                      },
                    ),
                    const SizedBox(width: 16),
                    Flexible(
                      child: Text(
                        widget.contentInfo.title,
                        style: Theme.of(
                          context,
                        ).textTheme.titleLarge?.copyWith(color: Colors.white),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                _isExpanded ? Icons.expand_less : Icons.expand_more,
                color: Colors.white,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExpandedContent() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: .1),
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(12)),
        border: Border(
          left: BorderSide(
            color: Colors.white.withValues(alpha: .2),
            width: 1.5,
          ),
          right: BorderSide(
            color: Colors.white.withValues(alpha: .2),
            width: 1.5,
          ),
          bottom: BorderSide(
            color: Colors.white.withValues(alpha: .2),
            width: 1.5,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: .1),
            blurRadius: 10,
            spreadRadius: 1,
          ),
        ],
        backgroundBlendMode: BlendMode.overlay,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 8),
          if (widget.content != null)
            ContentTree(
              content: widget.content!,
              iconPath: widget.contentInfo.icon,
              level: widget.level,
              chapter: widget.chapter,
              section: widget.contentInfo.section.name,
            ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }
}
