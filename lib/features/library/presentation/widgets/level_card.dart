import 'package:flutter/material.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/library/domain/models/level_info.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';

class LevelCard extends StatelessWidget {
  final LevelInfo data;
  final bool tapable;

  const LevelCard({super.key, required this.data, this.tapable = true});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final locale = Localizations.localeOf(context).languageCode;

    return RepaintBoundary(
      child: InkWell(
        onTap:
            !tapable
                ? null
                : () {
                  customNav(
                    context,
                    RouterName.libraryChapter,
                    params: {'level': data.level.toString()},
                  );
                },
        child: Container(
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(data.image),
              fit: BoxFit.cover,
            ),
          ),
          child: Stack(
            children: [
              Positioned(
                bottom: 15,
                right: 15,
                child: CircleAvatar(
                  radius: 30,
                  backgroundColor: const Color(
                    0xFFFFD4D6,
                  ).withValues(alpha: 0.5),
                  child: Text(
                    data.level.name,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 20, 30, 40),
                child: Align(
                  alignment: Alignment.centerRight,
                  child: ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: screenWidth * 0.6),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          data.title.getByLocale(locale),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          data.description.getByLocale(locale),
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            shadows: [
                              Shadow(
                                offset: const Offset(1.0, 1.0),
                                blurRadius: 3.0,
                                color: Colors.black.withValues(alpha: .5),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
