import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/shared/domain/models/chapter/chapter_data.dart';
import 'package:selfeng/shared/domain/models/level.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';

class LibraryChapterCard extends StatelessWidget {
  final ChapterData chapter;
  final Level level;
  final bool tapable;
  static const double _imageWidth = 0.3;
  static const double _contentWidth = 0.7;

  const LibraryChapterCard({
    super.key,
    required this.chapter,
    required this.level,
    this.tapable = true,
  });

  void _handleTap(BuildContext context) {
    if (!tapable) return;
    customNav(
      context,
      RouterName.libraryChapterContent,
      params: {'level': level.name, 'chapter': chapter.chapter.toString()},
    );
  }

  Widget _buildChapterNumber() {
    return CircleAvatar(
      radius: 25,
      backgroundColor: const Color(0xFFFFFFFF).withValues(alpha: .8),
      child: Text(
        chapter.chapter.toString(),
        style: const TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.bold,
          color: Color(0xFFC62828),
        ),
      ),
    );
  }

  Widget _buildImage(double screenWidth) {
    return CachedNetworkImage(
      placeholder: (context, url) => const LoadingCircle(),
      errorWidget: (context, url, error) => const Icon(Icons.error),
      fit: BoxFit.fitWidth,
      imageUrl: chapter.imageUrl,
      width: screenWidth * _imageWidth,
      memCacheWidth: (screenWidth * _imageWidth).toInt(),
    );
  }

  Widget _buildContent(
    BuildContext context,
    String locale,
    TextTheme textTheme,
  ) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 10, 10, 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            chapter.label,
            style: textTheme.titleMedium?.copyWith(
              color: Colors.white,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            locale == 'en' ? chapter.description.en : chapter.description.id,
            style: textTheme.titleSmall?.copyWith(color: Colors.white),
            overflow: TextOverflow.ellipsis,
            softWrap: true,
            maxLines: 8,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final locale = Localizations.localeOf(context).languageCode;
    final textTheme = Theme.of(context).textTheme;

    return InkWell(
      onTap: tapable ? () => _handleTap(context) : null,
      child: Container(
        color:
            chapter.chapter.isEven
                ? const Color(0xff93000F)
                : const Color(0xff380C0A),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: screenWidth * _imageWidth,
              child: Stack(
                children: [
                  _buildImage(screenWidth),
                  if (tapable)
                    Positioned(
                      top: 10,
                      left: (screenWidth * _imageWidth * 0.5) - 25,
                      child: _buildChapterNumber(),
                    ),
                ],
              ),
            ),
            SizedBox(
              width: screenWidth * _contentWidth,
              child: _buildContent(context, locale, textTheme),
            ),
          ],
        ),
      ),
    );
  }
}
