// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'content_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ContentInfo _$ContentInfoFromJson(Map<String, dynamic> json) => _ContentInfo(
  title: json['title'] as String,
  image: json['image'] as String,
  icon: json['icon'] as String,
  section: $enumDecode(_$SectionTypeEnumMap, json['section']),
  isExpanded: json['isExpanded'] as bool? ?? false,
);

Map<String, dynamic> _$ContentInfoToJson(_ContentInfo instance) =>
    <String, dynamic>{
      'title': instance.title,
      'image': instance.image,
      'icon': instance.icon,
      'section': _$SectionTypeEnumMap[instance.section]!,
      'isExpanded': instance.isExpanded,
    };

const _$SectionTypeEnumMap = {
  SectionType.pronunciation: 'pronunciation',
  SectionType.conversation: 'conversation',
  SectionType.listening: 'listening',
  SectionType.speaking: 'speaking',
};
