import 'package:flutter/material.dart';
import 'package:selfeng/features/dashboard/presentation/widgets/get_started_banner.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';

class GetStartedSection extends StatelessWidget {
  const GetStartedSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 10,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            context.loc.explore_your_potential,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
          ),
        ),
        const GetStartedBanner(),
      ],
    );
  }
}
