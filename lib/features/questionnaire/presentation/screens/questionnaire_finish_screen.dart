import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/questionnaire/presentation/providers/questionnaire_controller.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';

class QuestionnaireFinishScreen extends ConsumerStatefulWidget {
  const QuestionnaireFinishScreen({super.key});

  @override
  ConsumerState<QuestionnaireFinishScreen> createState() =>
      _QuestionnaireFinishScreenState();
}

class _QuestionnaireFinishScreenState
    extends ConsumerState<QuestionnaireFinishScreen> {
  late AsyncValue viewState;
  late QuestionnaireController viewModel;

  @override
  Widget build(BuildContext context) {
    viewState = ref.watch(questionnaireControllerProvider);
    viewModel = ref.watch(questionnaireControllerProvider.notifier);

    ref.listen(questionnaireControllerProvider.select((value) => value), ((
      previous,
      next,
    ) {
      //show Snackbar on failure
      next.maybeWhen(
        error: (error, track) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(error.toString())));
        },
        orElse: () {},
      );
    }));
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xffFFF2F2),
              Color(0xffFDD8D8),
              Color(0xffFFECEC),
              Color(0xffFFFFFF),
            ],
            begin: Alignment.bottomLeft,
            end: Alignment.topRight,
          ),
        ),
        padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
        child: ListView(
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: InkWell(
                onTap:
                    () => customNav(
                      context,
                      isReplace: true,
                      RouterName.questionnaireScreen,
                    ),
                child: Container(
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Color(0xffFFB3AC),
                  ),
                  height: 40,
                  width: 40,
                  child: const Icon(
                    Icons.chevron_left_rounded,
                    size: 36,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 68),
            Image.asset(
              '$assetImageQuestionnaire/BG-Done.png',
              // fit: BoxFit.fitWidth,
              width: 254,
              height: 252,
            ),
            const SizedBox(height: 20),
            Text(
              textAlign: TextAlign.center,
              context.loc.questionnaireFinish,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 20),
            Text(
              textAlign: TextAlign.center,
              context.loc.questionnaireFinishDesc,
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(color: const Color(0xff998E8D)),
            ),
            const SizedBox(height: 26),
            VButtonGradient(
              title: context.loc.questionnaireIWilling,
              onTap: () {
                viewModel.saveAnswer();
                customNav(
                  context,
                  RouterName.diagnosticTestOnboardScreen,
                  isReplace: true,
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
