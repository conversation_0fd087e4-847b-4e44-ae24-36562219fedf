// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:video_player/video_player.dart';

part 'main_lesson.freezed.dart';
part 'main_lesson.g.dart';

typedef MainLessonList = List<MainLesson>;

enum QuestionConcreteState { initial, answered }

enum SpeakingStage {
  stage1,
  onboardingStage2,
  stage2,
  onboardingStage3,
  stage3,
}

enum SpeakingSessionType { question, answer }

@freezed
sealed class ChapterIndexData with _$ChapterIndexData {
  factory ChapterIndexData({
    required int chapter,
    required String level,
    required String path,
  }) = _ChapterIndexData;
  factory ChapterIndexData.fromJson(Map<String, dynamic> json) =>
      _$ChapterIndexDataFromJson(json);
}

@freezed
sealed class ContentIndexData with _$ContentIndexData {
  factory ContentIndexData({
    @Default('') @JsonKey(name: 'content_title') String title,
    @Default('') @<PERSON>son<PERSON>ey(name: 'content_path') String contentPath,
    @Default(1) @Json<PERSON>ey(name: 'content_order') int contentOrder,
    @JsonKey(name: 'part_order') int? partOrder,
    @JsonKey(name: 'part_title') String? partTitle,
    @JsonKey(name: 'subpart_order') int? subpartOrder,
    @JsonKey(name: 'subpart_title') String? subpartTitle,
    @Default(false) bool hasResult,
    @Default(false) bool firstStage,
    @Default(false) bool secondStage,
    @Default(false) bool thirdStage,
  }) = _ContentIndexData;

  factory ContentIndexData.fromJson(Map<String, dynamic> json) =>
      _$ContentIndexDataFromJson(json);
}

@freezed
sealed class MainLesson with _$MainLesson {
  factory MainLesson({
    @Default(QuestionConcreteState.initial)
    @JsonKey(includeToJson: false)
    QuestionConcreteState state,
    @JsonKey(name: 'question_id') String? questionId,
    @Default(0) @JsonKey(includeToJson: false) int order,
    @Default('')
    @JsonKey(name: 'correct_answer', includeToJson: false)
    String correctAnswer,
    @Default('') @JsonKey(includeToJson: false) String question,
    @Default('') String answer,
    @Default(false) @JsonKey(name: 'is_correct') bool isCorrect,
  }) = _MainLesson;

  factory MainLesson.fromJson(dynamic json) => _$MainLessonFromJson(json);
}

@freezed
sealed class PronunciationSubPart with _$PronunciationSubPart {
  const factory PronunciationSubPart({
    @JsonKey(name: "audio_url") required String audio,
    @JsonKey(name: "image_url") required String image,
    @JsonKey(name: "caption") required String caption,
    String? description,
    @JsonKey(name: "order") required int order,
  }) = _PronunciationSubPart;

  factory PronunciationSubPart.fromJson(Map<String, dynamic> json) =>
      _$PronunciationSubPartFromJson(json);
}

@freezed
sealed class ConversationPart with _$ConversationPart {
  const factory ConversationPart({
    @JsonKey(name: "order") int? order,
    @JsonKey(name: "title") String? title,
    @JsonKey(name: "video_url") String? video,
    @JsonKey(name: "video_controller", fromJson: videoControllerFromJson)
    videoController,
    @JsonKey(name: "video_meta") VideoMeta? videoMeta,
    @JsonKey(name: "img_url") String? image,
  }) = _ConversationPart;

  factory ConversationPart.fromJson(Map<String, dynamic> json) =>
      _$ConversationPartFromJson(json);
}

@freezed
sealed class VideoMeta with _$VideoMeta {
  const factory VideoMeta({
    @JsonKey(name: "duration", fromJson: durationFromJson) Duration? duration,
    @Default(false) bool isPlayed,
  }) = _VideoMeta;

  factory VideoMeta.fromJson(Map<String, dynamic> json) =>
      _$VideoMetaFromJson(json);
}

@freezed
sealed class AudioPath with _$AudioPath {
  const factory AudioPath({
    @Default('') String path,
    @Default('') String refPath,
    @Default('') String url,
  }) = _AudioPath;

  factory AudioPath.fromJson(Map<String, dynamic> json) =>
      _$AudioPathFromJson(json);
}

@freezed
sealed class ListeningPart with _$ListeningPart {
  const factory ListeningPart({
    @JsonKey(name: "audio_url") String? main,
    @JsonKey(name: "order") int? order,
    @JsonKey(name: "title") String? title,
    @JsonKey(name: "image_url") String? image,
    @Default([]) @JsonKey(name: "questions") List<Question> questions,
  }) = _ListeningPart;

  factory ListeningPart.fromJson(Map<String, dynamic> json) =>
      _$ListeningPartFromJson(json);
}

@freezed
sealed class Question with _$Question {
  const factory Question({
    @JsonKey(name: "collapse") bool? collapse,
    @JsonKey(name: "order") int? order,
    @JsonKey(name: "question") String? question,
    String? answer,
    bool? isCorrect,
    @Default([]) @JsonKey(name: "choices") List<Choice> choices,
  }) = _Question;

  factory Question.fromJson(Map<String, dynamic> json) =>
      _$QuestionFromJson(json);
}

@freezed
sealed class Choice with _$Choice {
  const factory Choice({
    @JsonKey(name: "is_correct") bool? isCorrect,
    @JsonKey(name: "text") String? text,
  }) = _Choice;

  factory Choice.fromJson(Map<String, dynamic> json) => _$ChoiceFromJson(json);
}

@freezed
sealed class SpeakingPart with _$SpeakingPart {
  const factory SpeakingPart({
    @JsonKey(name: "answer") required Answer answer,
    @JsonKey(name: "question") required Answer question,
    @JsonKey(name: "image_url") required String image,
    @JsonKey(name: "imageB_url") required String imageB,
    @JsonKey(name: "order") required int order,
    @JsonKey(name: "title") required String title,
  }) = _SpeakingPart;

  factory SpeakingPart.fromJson(Map<String, dynamic> json) =>
      _$SpeakingPartFromJson(json);
}

@freezed
sealed class Answer with _$Answer {
  const factory Answer({
    @JsonKey(name: "audio_url") required String audio,
    @JsonKey(name: "text") required String text,
    @Default(false) bool isActive,
  }) = _Answer;

  factory Answer.fromJson(Map<String, dynamic> json) => _$AnswerFromJson(json);
}

VideoPlayerController videoControllerFromJson(
  value,
) => VideoPlayerController.networkUrl(
  Uri.parse(
    value ??
        'https://firebasestorage.googleapis.com/v0/b/selfeng-dev.appspot.com/o/video%2Ftmp%2Ftmpvideo.mp4?alt=media&token=423dc215-6ccf-4c16-9971-a6306f998f10',
  ),
);
Duration durationFromJson(value) {
  int duration = int.parse(value.split('.')[0]);
  // List<String> timeParts = value.split('.')[0].split(':');
  return Duration(
    hours: (duration / 360).floor(),
    minutes: (duration / 60).floor(),
    seconds: duration.round() % 60,
  );
}
