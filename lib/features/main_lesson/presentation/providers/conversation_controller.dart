import 'dart:convert';

import 'package:flick_video_player/flick_video_player.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/features/library/presentation/providers/chapter_content_provider.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/features/main_lesson/domain/repositories/main_lesson_repository.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/conversation_state.dart';
import 'package:selfeng/features/main_lesson/domain/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/main_lesson_state.dart';
import 'package:selfeng/services/user_data_service/domain/providers/user_data_service_provider.dart';
import 'package:selfeng/services/user_data_service/domain/repositories/user_data_service_repository.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:video_player/video_player.dart';

part 'conversation_controller.g.dart';

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
@riverpod
class ConversationController extends _$ConversationController {
  late MainLessonRepository mainLessonRepository;
  late UserDataServiceRepository _userDataServiceRepository;
  late ChapterContentStateNotifier _chapterContentStateNotifier;

  List<ContentIndexData> _paths = [];
  Set<String> _completedPaths = {};

  late MainLessonState _mainLessonState;

  @override
  FutureOr<ConversationState> build(
    String level,
    String chapter,
    String path,
  ) async {
    mainLessonRepository = ref.watch(mainLessonRepositoryProvider);
    _userDataServiceRepository = ref.watch(userDataServiceProvider);
    _chapterContentStateNotifier = ref.watch(
      chapterContentStateProvider.notifier,
    );

    late bool isIntro;

    await mainLessonRepository.isIntro(lessonName: 'conversation').then((val) {
      val.fold(
        (failure) {
          state = AsyncError(failure.message, StackTrace.current);
        },
        (data) async {
          isIntro = data;
        },
      );
    });
    init(level, chapter, path);
    return ConversationState(
      flickManager: FlickManager(
        videoPlayerController: VideoPlayerController.networkUrl(
          Uri.parse(
            'https://firebasestorage.googleapis.com/v0/b/selfeng-dev.appspot.com/o/video%2Ftmp%2Ftmpvideo.mp4?alt=media&token=423dc215-6ccf-4c16-9971-a6306f998f10',
          ),
        ),
      ),
    );
  }

  Future<void> init(String level, String chapter, String path) async {
    final contents = await mainLessonRepository.getPathIndex(
      level: level,
      chapter: chapter,
      section: SectionType.conversation,
    );
    await mainLessonRepository.saveIntro(lessonName: 'conversation');
    contents.fold(
      (failure) {
        state = AsyncError(failure.message, StackTrace.current);
      },
      (data) async {
        _paths = data;

        final result = await _userDataServiceRepository.getConversationResult(
          PronunciationScoreParams(level: level, chapter: chapter),
        );

        result.fold(
          (failure) {
            state = AsyncError(failure.message, StackTrace.current);
          },
          (data) {
            _completedPaths = data.map((e) => e.path).toSet();
          },
        );

        _mainLessonState = ref.read(mainLessonStateProvider);
        if (path != 'blankpath') {
          final contentPath = utf8.decode(base64Url.decode(path));
          state = AsyncData(
            state.value!.copyWith(
              selectedIndex: _paths.indexWhere(
                (element) => element.contentPath == contentPath,
              ),
            ),
          );
        } else if (_mainLessonState.fromLastCourse == true &&
            _mainLessonState.lastConversation != null) {
          state = AsyncData(
            state.value!.copyWith(
              selectedIndex: _paths.indexWhere(
                (element) =>
                    element.contentPath ==
                    _mainLessonState.lastConversation!.path,
              ),
            ),
          );
        }

        initContent();
      },
    );
  }

  Future<void> initContent() async {
    final result = await mainLessonRepository.getConversationList(
      _paths.map((e) => e.contentPath).toList(),
    );

    result.fold(
      (failure) {
        state = AsyncError(failure.message, StackTrace.current);
      },
      (data) {
        state = AsyncData(state.value!.copyWith(conversations: data));
        if (data.isNotEmpty) {
          selectVideo(state.value!.selectedIndex);
        }
      },
    );
  }

  Future<void> selectVideo(int index) async {
    if (index < 0 || index >= state.value!.conversations.length) return;
    state.value?.flickManager?.handleChangeVideo(
      VideoPlayerController.networkUrl(
        Uri.parse(state.value?.conversations[index].video ?? ''),
      ),
    );

    List<ConversationPart> tempData = List.from(state.value!.conversations);
    tempData[index] = tempData[index].copyWith(
      videoMeta: tempData[index].videoMeta?.copyWith(isPlayed: true),
    );

    state = AsyncData(
      state.value!.copyWith(selectedIndex: index, conversations: tempData),
    );
    await saveLastCourse(index);
    isNextSection();
  }

  Future<void> saveLastCourse(int index) async {
    final data = LastCourse(
      accessTime: DateTime.now().toUtc(),
      level: level,
      chapter: int.parse(chapter),
      section: SectionType.conversation,
      path: _paths[index].contentPath,
    );

    await _userDataServiceRepository.updateLastCourse(
      lastCourse: data,
      section: SectionType.conversation,
    );
    final mainLessonStateNotifier = ref.watch(mainLessonStateProvider.notifier);
    mainLessonStateNotifier.updateLastConversation(data);
    mainLessonStateNotifier.updateFromLastCourse(false);
  }

  Future<void> markSectionAsCompleted() async {
    // Use set operations for O(n) performance instead of O(n*m)
    // Create a set of required paths and check if it's a subset of completed paths
    final requiredPaths =
        _paths.map((pathData) => pathData.contentPath).toSet();
    final allPathsCompleted = requiredPaths.difference(_completedPaths).isEmpty;

    if (allPathsCompleted) {
      await _userDataServiceRepository.setSectionCompleted(
        level: level,
        chapter: chapter,
        section: SectionType.conversation,
      );
    }
  }

  Future<void> saveResult() async {
    final result = await _userDataServiceRepository.saveLessonResult(
      level: level,
      chapter: chapter,
      section: SectionType.conversation,
      result: LessonResult(
        contentOrder: _paths[state.value!.selectedIndex].contentOrder,
        path: _paths[state.value!.selectedIndex].contentPath,
        result: {},
      ),
    );

    result.fold(
      (failure) {
        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) {
        _completedPaths.add(_paths[state.value!.selectedIndex].contentPath);
        _chapterContentStateNotifier.updateConversation(
          _paths[state.value!.selectedIndex],
        );
        markSectionAsCompleted();
      },
    );
  }

  AsyncData<ConversationState> isNextSection() =>
      state = AsyncData(
        state.value!.copyWith(
          nextSection:
              state.value!.selectedIndex == _paths.length - 1 ? true : false,
        ),
      );
}

/// Simple mock of a 401 exception
class UnauthorizedException implements Exception {
  const UnauthorizedException(this.message);
  final String message;
}

/// Mock of the duration of a network request
final networkRoundTripTime = 2.seconds;
