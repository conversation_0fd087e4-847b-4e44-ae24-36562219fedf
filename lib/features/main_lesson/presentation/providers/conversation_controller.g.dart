// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversation_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$conversationControllerHash() =>
    r'237eb507ab329fc78cfb6a1573cb49fc04dc48a9';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$ConversationController
    extends BuildlessAutoDisposeAsyncNotifier<ConversationState> {
  late final String level;
  late final String chapter;
  late final String path;

  FutureOr<ConversationState> build(String level, String chapter, String path);
}

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
///
/// Copied from [ConversationController].
@ProviderFor(ConversationController)
const conversationControllerProvider = ConversationControllerFamily();

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
///
/// Copied from [ConversationController].
class ConversationControllerFamily
    extends Family<AsyncValue<ConversationState>> {
  /// This controller is an [AsyncNotifier] that holds and handles our authentication state
  ///
  /// Copied from [ConversationController].
  const ConversationControllerFamily();

  /// This controller is an [AsyncNotifier] that holds and handles our authentication state
  ///
  /// Copied from [ConversationController].
  ConversationControllerProvider call(
    String level,
    String chapter,
    String path,
  ) {
    return ConversationControllerProvider(level, chapter, path);
  }

  @override
  ConversationControllerProvider getProviderOverride(
    covariant ConversationControllerProvider provider,
  ) {
    return call(provider.level, provider.chapter, provider.path);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'conversationControllerProvider';
}

/// This controller is an [AsyncNotifier] that holds and handles our authentication state
///
/// Copied from [ConversationController].
class ConversationControllerProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          ConversationController,
          ConversationState
        > {
  /// This controller is an [AsyncNotifier] that holds and handles our authentication state
  ///
  /// Copied from [ConversationController].
  ConversationControllerProvider(String level, String chapter, String path)
    : this._internal(
        () =>
            ConversationController()
              ..level = level
              ..chapter = chapter
              ..path = path,
        from: conversationControllerProvider,
        name: r'conversationControllerProvider',
        debugGetCreateSourceHash:
            const bool.fromEnvironment('dart.vm.product')
                ? null
                : _$conversationControllerHash,
        dependencies: ConversationControllerFamily._dependencies,
        allTransitiveDependencies:
            ConversationControllerFamily._allTransitiveDependencies,
        level: level,
        chapter: chapter,
        path: path,
      );

  ConversationControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.level,
    required this.chapter,
    required this.path,
  }) : super.internal();

  final String level;
  final String chapter;
  final String path;

  @override
  FutureOr<ConversationState> runNotifierBuild(
    covariant ConversationController notifier,
  ) {
    return notifier.build(level, chapter, path);
  }

  @override
  Override overrideWith(ConversationController Function() create) {
    return ProviderOverride(
      origin: this,
      override: ConversationControllerProvider._internal(
        () =>
            create()
              ..level = level
              ..chapter = chapter
              ..path = path,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        level: level,
        chapter: chapter,
        path: path,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<
    ConversationController,
    ConversationState
  >
  createElement() {
    return _ConversationControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ConversationControllerProvider &&
        other.level == level &&
        other.chapter == chapter &&
        other.path == path;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, level.hashCode);
    hash = _SystemHash.combine(hash, chapter.hashCode);
    hash = _SystemHash.combine(hash, path.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ConversationControllerRef
    on AutoDisposeAsyncNotifierProviderRef<ConversationState> {
  /// The parameter `level` of this provider.
  String get level;

  /// The parameter `chapter` of this provider.
  String get chapter;

  /// The parameter `path` of this provider.
  String get path;
}

class _ConversationControllerProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          ConversationController,
          ConversationState
        >
    with ConversationControllerRef {
  _ConversationControllerProviderElement(super.provider);

  @override
  String get level => (origin as ConversationControllerProvider).level;
  @override
  String get chapter => (origin as ConversationControllerProvider).chapter;
  @override
  String get path => (origin as ConversationControllerProvider).path;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
