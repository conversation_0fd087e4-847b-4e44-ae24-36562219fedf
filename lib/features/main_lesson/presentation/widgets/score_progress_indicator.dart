import 'package:flutter/material.dart';
import 'dart:math' as math;

class ScoreProgressIndicator extends StatelessWidget {
  final int progress;
  final double strokeWidth;
  final double width;
  final double height;
  final bool isHalfCircle;

  const ScoreProgressIndicator({
    super.key,
    required this.progress,
    this.strokeWidth = 20.0,
    this.width = 200.0,
    this.height = 100.0,
    this.isHalfCircle = true,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: isHalfCircle ? height : width,
      child: Stack(
        children: [
          if (!isHalfCircle)
            Align(
              alignment: Alignment.center,
              child: Text(
                '$progress/100',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: progress > 70 ? Color(0xff36AA34) : Color(0xff93000F),
                ),
              ),
            ),
          SizedBox(
            width: width,
            height: isHalfCircle ? height : width,
            child: CustomPaint(
              painter: _CurvedProgressPainter(
                progress: progress / 100,
                backgroundColor: Colors.grey,
                progressColor:
                    progress > 70 ? Color(0xff36AA34) : Color(0xff93000F),
                strokeWidth: strokeWidth,
                isHalfCircle: isHalfCircle,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _CurvedProgressPainter extends CustomPainter {
  final double progress;
  final Color backgroundColor;
  final Color progressColor;
  final double strokeWidth;
  final bool isHalfCircle;

  _CurvedProgressPainter({
    required this.progress,
    required this.backgroundColor,
    required this.progressColor,
    required this.strokeWidth,
    required this.isHalfCircle,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint backgroundPaint =
        Paint()
          ..color = backgroundColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = strokeWidth
          ..strokeCap = StrokeCap.round;

    final Paint progressPaint =
        Paint()
          ..color = progressColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = strokeWidth
          ..strokeCap = StrokeCap.round;

    final Rect rect =
        isHalfCircle
            ? Rect.fromLTRB(
              strokeWidth / 2,
              strokeWidth / 2,
              size.width - strokeWidth / 2,
              size.height * 2 - strokeWidth / 2,
            )
            : Rect.fromLTRB(
              strokeWidth / 2,
              strokeWidth / 2,
              size.width - strokeWidth / 2,
              size.height - strokeWidth / 2,
            );

    if (isHalfCircle) {
      // Half circle mode (unchanged)
      canvas.drawArc(rect, math.pi, math.pi, false, backgroundPaint);

      canvas.drawArc(rect, math.pi, math.pi * progress, false, progressPaint);
    } else {
      // Full circle mode (clockwise from top)
      canvas.drawArc(
        rect,
        math.pi / 2, // Start from bottom
        math.pi * 2,
        false,
        backgroundPaint,
      );

      canvas.drawArc(
        rect,
        math.pi / 2, // Start from bottom
        math.pi * 2 * progress,
        false,
        progressPaint,
      );
    }
  }

  @override
  bool shouldRepaint(_CurvedProgressPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.backgroundColor != backgroundColor ||
        oldDelegate.progressColor != progressColor ||
        oldDelegate.strokeWidth != strokeWidth ||
        oldDelegate.isHalfCircle != isHalfCircle;
  }
}
