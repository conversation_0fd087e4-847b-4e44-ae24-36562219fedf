import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/services/setting_cache_service/domain/providers/setting_cache_provider.dart';
import 'package:selfeng/services/user_data_service/domain/providers/user_data_service_provider.dart';
import 'package:selfeng/shared/domain/models/level.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';

class CertificateNotificationScreen extends ConsumerStatefulWidget {
  final String level;

  const CertificateNotificationScreen({super.key, required this.level});

  @override
  ConsumerState<CertificateNotificationScreen> createState() =>
      _CertificateNotificationScreenState();
}

class _CertificateNotificationScreenState
    extends ConsumerState<CertificateNotificationScreen> {
  late final AudioPlayer _player;
  bool? _isLevelCompleted;

  @override
  void initState() {
    super.initState();
    _player = AudioPlayer();
    _checkLevelCompletion();
  }

  @override
  void dispose() {
    _player.dispose();
    super.dispose();
  }

  Future<void> _checkLevelCompletion() async {
    final result = await ref
        .read(userDataServiceProvider)
        .isLevelCompleted(
          level: Level.values.byName(widget.level.toLowerCase()),
        );
    result.fold((l) => setState(() => _isLevelCompleted = false), (r) {
      if (r) {
        _playCongratsSound();
      }
      setState(() => _isLevelCompleted = r);
    });
  }

  /// Plays a congratulatory sound effect if audio is enabled globally.
  Future<void> _playCongratsSound() async {
    final isAudioEnabled = ref.read(audioToggleProvider);

    // Only play if audio is enabled
    if (isAudioEnabled) {
      // Ensure the player is stopped before playing, in case of quick rebuilds
      await _player.stop();
      // Use try-catch for robustness, e.g., if asset is missing
      try {
        await _player.play(
          AssetSource('sounds/mixkit-game-level-completed-2059.wav'),
        );
      } catch (e) {
        debugPrint("Error playing sound: $e");
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xffFFF2F2),
              Color(0xffFDD8D8),
              Color(0xffFFFFFF),
              Color(0xffFFFFFF),
            ],
            begin: Alignment.bottomLeft,
            end: Alignment.topRight,
          ),
        ),
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLevelCompleted == null) {
      return const Center(child: CircularProgressIndicator());
    } else if (_isLevelCompleted!) {
      return _buildCongratsView();
    } else {
      return _buildIncompleteView();
    }
  }

  Widget _buildCongratsView() {
    return Stack(
      children: [
        ListView(
          padding: const EdgeInsets.only(bottom: 120),
          children: [
            const SizedBox(height: 200),
            Image.asset(
              'assets/images/certificate/${widget.level.toLowerCase()}.png',
              width: 254,
              height: 254,
              errorBuilder: (context, error, stackTrace) {
                return const Icon(
                  Icons.image_not_supported,
                  size: 254,
                  color: Colors.grey,
                );
              },
            ),
            const SizedBox(height: 20),
            Text(
              _getCertNotifText(
                Level.values.byName(widget.level.toLowerCase()),
              ),
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 24),
            _buildCertificateDownloadWidget(),
          ],
        ),
        Align(
          alignment: Alignment.bottomCenter,
          child: Padding(
            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 40),
            child: SizedBox(
              height: 60,
              child: VButtonGradient(
                title: 'Continue',
                onTap: () {
                  _showNextSectionDialog(context);
                },
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildIncompleteView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.warning_amber_rounded,
            size: 100,
            color: Colors.orange,
          ),
          const SizedBox(height: 20),
          Text(
            context.loc.level_not_completed,
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32.0),
            child: Text(
              context.loc.level_not_completed_desc,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ),
          const SizedBox(height: 40),
          SizedBox(
            width: 200,
            height: 60,
            child: VButtonGradient(
              title: context.loc.back_to_lessons,
              onTap: () => context.pop(),
            ),
          ),
        ],
      ),
    );
  }

  /// Shows a dialog asking the user if they want to proceed to the next chapter.
  void _showNextSectionDialog(BuildContext context) {
    VDialogAlert(
      title: context.loc.nextSection,
      child: Column(
        mainAxisSize: MainAxisSize.min, // Prevent excessive dialog height
        children: [
          VButtonGradient(
            title: context.loc.yes,
            fontStyle: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.white),
            onTap: () {
              // Pop the dialog first
              context.pop();
              // Navigate to the next level pronunciation title screen
              customNav(
                context,
                RouterName.chapterTitle,
                params: {
                  'level':
                      _getNextLevel(
                        // 6. Access 'level' via the widget property.
                        Level.values.byName(widget.level.toLowerCase()),
                      ).name,
                  'chapter': '1',
                },
                isReplace: true, // Replace the current screen in the stack
              );
            },
          ),
          const SizedBox(height: 16), // Reduced spacing for consistency
          VButtonGradient(
            title: context.loc.no,
            fontStyle: Theme.of(context).textTheme.bodyLarge,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(14),
              color: Colors.transparent,
              border: Border.all(color: const Color(0xff802115), width: 0.6),
            ),
            onTap: () => context.pop(),
          ),
        ],
      ),
    ).showMyDialog(context);
  }

  Level _getNextLevel(Level level) {
    switch (level) {
      case Level.a1:
        return Level.a2;
      case Level.a2:
        return Level.b1;
      case Level.b1:
        return Level.b2;
      case Level.b2:
        return Level.c1;
      case Level.c1:
        return Level.c2;
      case Level.c2:
        // Or handle this case by maybe navigating to a "course complete" screen
        return Level.c2;
    }
  }

  String _getCertNotifText(Level level) {
    switch (level) {
      case Level.a1:
        return context.loc.cert_notif_a1;
      case Level.a2:
        return context.loc.cert_notif_a2;
      case Level.b1:
        return context.loc.cert_notif_b1;
      case Level.b2:
        return context.loc.cert_notif_b2;
      case Level.c1:
        return context.loc.cert_notif_c1;
      case Level.c2:
        return context.loc.cert_notif_c2;
    }
  }

  String _getCertDownloadText(Level level) {
    switch (level) {
      case Level.a1:
        return context.loc.cert_download_a1;
      case Level.a2:
        return context.loc.cert_download_a2;
      case Level.b1:
        return context.loc.cert_download_b1;
      case Level.b2:
        return context.loc.cert_download_b2;
      case Level.c1:
        return context.loc.cert_download_c1;
      case Level.c2:
        return context.loc.cert_download_c2;
    }
  }

  Widget _buildCertificateDownloadWidget() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: GestureDetector(
        onTap: () {
          customNav(context, RouterName.profilesettingScreen);
        },
        child: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFFE8F5E8), Color(0xFFF0F8F0)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.download_rounded,
                  color: Color(0xFF2E7D32),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  _getCertDownloadText(
                    Level.values.byName(widget.level.toLowerCase()),
                  ),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: const Color(0xFF2E7D32),
                    fontWeight: FontWeight.w500,
                    height: 1.4,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              const Icon(
                Icons.arrow_forward_ios_rounded,
                color: Color(0xFF2E7D32),
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
