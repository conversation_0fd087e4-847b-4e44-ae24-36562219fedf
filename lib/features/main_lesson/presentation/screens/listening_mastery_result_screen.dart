import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/listening_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/listening_state.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/next_button.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';
import 'package:selfeng/services/setting_cache_service/domain/providers/setting_cache_provider.dart'; // Added import

class ListeningMasteryResultScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  final String path;
  const ListeningMasteryResultScreen({
    super.key,
    required this.level,
    required this.chapter,
    required this.path,
  });

  @override
  ConsumerState<ListeningMasteryResultScreen> createState() =>
      _ListeningMasteryResultScreenState();
}

class _ListeningMasteryResultScreenState
    extends ConsumerState<ListeningMasteryResultScreen>
    with TickerProviderStateMixin {
  late AsyncValue<ListeningState> viewState;
  late ListeningController viewModel;
  late final AudioPlayer _bgmPlayer = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _playCongrats();
  }

  @override
  void dispose() {
    _bgmPlayer.dispose();
    super.dispose();
  }

  Future<void> _playCongrats() async {
    // Read the audio toggle state
    final isAudioEnabled = ref.read(audioToggleProvider);
    // Only play if audio is enabled
    if (isAudioEnabled) {
      await _bgmPlayer.play(AssetSource('sounds/score.mp3'));
    }
  }

  @override
  Widget build(BuildContext context) {
    final prov = ListeningControllerProvider(
      widget.level,
      widget.chapter,
      widget.path,
    );

    viewState = ref.watch(prov);
    viewModel = ref.watch(prov.notifier);
    final totalCorrect = viewModel.calculateTotalCorrectPart();

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xffFFF2F2),
              Color(0xffFDD8D8),
              Color(0xffFFECEC),
              Color(0xffFFFFFF),
            ],
            begin: Alignment.bottomLeft,
            end: Alignment.topRight,
          ),
        ),
        child: Stack(
          children: [
            if (viewState.value!.listenings.isNotEmpty)
              SingleChildScrollView(
                child: Column(
                  children: [
                    SizedBox(height: 100),
                    Container(
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xff682000), Color(0xff490206)],
                          begin: Alignment.bottomLeft,
                          end: Alignment.topRight,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 5.5,
                        vertical: 2,
                      ),
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        context.loc.evaluation_results,
                        style: Theme.of(context).textTheme.headlineMedium
                            ?.copyWith(color: Colors.white),
                      ),
                    ),
                    const SizedBox(height: 42),
                    Center(
                      child: Text(
                        context.loc.your_score,
                        style: Theme.of(context).textTheme.headlineSmall,
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 28),
                    Center(
                      child: Container(
                        height: 146,
                        width: 146,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              Color(0xffFE754C),
                              Color(0xffE21F29),
                              Color(0xffC3151F),
                            ],
                            begin: Alignment.bottomLeft,
                            end: Alignment.topRight,
                          ),
                          image: DecorationImage(
                            image: AssetImage(
                              '$assetImageMainLesson/pronunciation_challenge/Skor-100.png',
                            ),
                            fit: BoxFit.scaleDown,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            '${viewModel.calculateTotalScorePart()}',
                            style: Theme.of(
                              context,
                            ).textTheme.headlineLarge?.copyWith(
                              fontSize: 60.0,
                              fontWeight: FontWeight.w700,
                              height: 1.25,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                    Center(
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Color(0xff421507), Color(0xffCA1E23)],
                            begin: Alignment.bottomLeft,
                            end: Alignment.topRight,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 5.5,
                          vertical: 2,
                        ),
                        margin: const EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          _getFeedbackText(context, totalCorrect),
                          style: Theme.of(
                            context,
                          ).textTheme.titleLarge?.copyWith(color: Colors.white),
                        ),
                      ),
                    ),
                    const SizedBox(height: 36),
                    Container(
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xff682000), Color(0xff490206)],
                          begin: Alignment.bottomLeft,
                          end: Alignment.topRight,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 5.5,
                        vertical: 2,
                      ),
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        '${context.loc.score_details}:',
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(color: Colors.white),
                      ),
                    ),
                    const SizedBox(height: 20),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: .4),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.white),
                      ),
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 24,
                      ),
                      child: Column(
                        children: [
                          Container(
                            color: Colors.white,
                            margin: const EdgeInsets.only(bottom: 24),
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      '${context.loc.correct}:',
                                      style:
                                          Theme.of(
                                            context,
                                          ).textTheme.titleMedium,
                                    ),
                                    Text(
                                      '${viewModel.calculateTotalCorrectPart()}/${viewState.value!.currentListenings.questions.length}',
                                      style: Theme.of(
                                        context,
                                      ).textTheme.titleMedium?.copyWith(
                                        color: const Color(0xff35BF32),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 16),
                                LinearProgressIndicator(
                                  value:
                                      viewModel.calculateTotalCorrectPart() /
                                      viewState
                                          .value!
                                          .currentListenings
                                          .questions
                                          .length,
                                  backgroundColor: const Color(0xffEDE0DE),
                                  valueColor: const AlwaysStoppedAnimation(
                                    Color(0xff6EFF6B),
                                  ),
                                  minHeight: 16,
                                ),
                              ],
                            ),
                          ),
                          Container(
                            color: Colors.white,
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      '${context.loc.wrong}:',
                                      style:
                                          Theme.of(
                                            context,
                                          ).textTheme.titleMedium,
                                    ),
                                    Text(
                                      '${viewState.value!.currentListenings.questions.length - viewModel.calculateTotalCorrectPart()}/${viewState.value!.currentListenings.questions.length}',
                                      style: Theme.of(
                                        context,
                                      ).textTheme.titleMedium?.copyWith(
                                        color: const Color(0xff35BF32),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 16),
                                LinearProgressIndicator(
                                  value:
                                      (viewState
                                              .value!
                                              .currentListenings
                                              .questions
                                              .length -
                                          viewModel
                                              .calculateTotalCorrectPart()) /
                                      viewState
                                          .value!
                                          .currentListenings
                                          .questions
                                          .length,
                                  backgroundColor: const Color(0xffEDE0DE),
                                  valueColor: const AlwaysStoppedAnimation(
                                    Color(0xff93000F),
                                  ),
                                  minHeight: 16,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 20),
                    NextButton(
                      onTap: () {
                        viewModel.nextPart(context);
                      },
                    ),
                  ],
                ),
              ),

            if (viewState.value!.nextSection)
              VDialogAlert(
                title: context.loc.nextSection,
                child: Column(
                  children: [
                    VButtonGradient(
                      title: context.loc.yes,
                      fontStyle: Theme.of(
                        context,
                      ).textTheme.bodyLarge?.copyWith(color: Colors.white),
                      onTap: () {
                        customNav(
                          context,
                          RouterName.speakingArenaOnboarding,
                          isReplace: true,
                          params: {
                            'level': widget.level,
                            'chapter': widget.chapter,
                          },
                        );
                      },
                    ),
                    const SizedBox(height: 24),
                    VButtonGradient(
                      title: context.loc.no,
                      fontStyle: Theme.of(context).textTheme.bodyLarge,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(14),
                        color: Colors.transparent,
                        border: Border.all(
                          color: const Color(0xff802115),
                          width: 0.6,
                        ),
                      ),
                      onTap: () {
                        context.pop();
                      },
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  String _getFeedbackText(BuildContext context, int totalCorrect) {
    if (totalCorrect == 5) return context.loc.excellent;
    if (totalCorrect == 4) return context.loc.great_job;
    if (totalCorrect == 3) return context.loc.good_effort;
    if (totalCorrect == 2) return context.loc.needs_improvement;
    return context.loc.keep_practicing;
  }
}
